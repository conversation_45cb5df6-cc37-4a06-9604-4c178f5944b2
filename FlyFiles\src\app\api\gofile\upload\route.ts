import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Create FormData for GoFile.io API
    const goFileFormData = new FormData();
    goFileFormData.append('file', file);

    // Add authentication token and account ID
    const accountToken = process.env.GOFILE_ACCOUNT_TOKEN;
    const accountId = process.env.GOFILE_ACCOUNT_ID;

    console.log('Account Token:', accountToken ? 'Present' : 'Missing');
    console.log('Account ID:', accountId ? 'Present' : 'Missing');

    if (accountToken) {
      goFileFormData.append('token', accountToken);
    }

    // Try to get the root folder for the account first
    let folderId = null;
    if (accountToken && accountId) {
      try {
        const accountResponse = await fetch(`https://api.gofile.io/accounts/${accountId}?token=${accountToken}`);
        const responseText = await accountResponse.text();

        try {
          const accountData = JSON.parse(responseText);
          console.log('Account details response:', accountData);

          if (accountData.status === 'ok' && accountData.data?.rootFolder) {
            folderId = accountData.data.rootFolder;
            goFileFormData.append('folderId', folderId);
            console.log('Using folder ID:', folderId);
          }
        } catch (parseError) {
          console.log('Failed to parse account response:', responseText);
        }
      } catch (err) {
        console.log('Failed to get account details:', err);
      }
    }

    // First get the best server for upload
    let uploadServer = 'store1.gofile.io';
    try {
      const serverResponse = await fetch('https://api.gofile.io/servers');
      const serverData = await serverResponse.json();
      console.log('Server response:', serverData);

      if (serverData.status === 'ok' && serverData.data?.servers?.length > 0) {
        // Use the first available server
        uploadServer = serverData.data.servers[0].name + '.gofile.io';
      }
    } catch (err) {
      console.log('Failed to get servers, using default:', err);
    }

    // Upload to GoFile.io - use the correct endpoint from documentation
    const uploadUrl = `https://${uploadServer}/contents/uploadfile`;
    console.log('Uploading to:', uploadUrl);

    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: goFileFormData,
    });

    const result = await response.json();
    console.log('GoFile.io response:', result);

    if (!response.ok) {
      return NextResponse.json(
        { error: 'GoFile.io upload failed', details: result },
        { status: response.status }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QVtdNAfSleeZti/ILZ5cbIW6f+KOYnviOXXJ1S2Ph5I=", "__NEXT_PREVIEW_MODE_ID": "918842f683d266e487ca3d0597961d27", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "57780c7d518f043c51b8b3d1d1e470715fdc23081d10ce56fdd868e9a34bd320", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b80df0322a96e5c8c028a4e4f69c01251e70533bbe16f7adc2831e2b3af5e809"}}}, "functions": {}, "sortedMiddleware": ["/"]}
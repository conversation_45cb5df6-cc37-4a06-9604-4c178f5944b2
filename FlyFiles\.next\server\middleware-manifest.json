{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QVtdNAfSleeZti/ILZ5cbIW6f+KOYnviOXXJ1S2Ph5I=", "__NEXT_PREVIEW_MODE_ID": "17ddd8f21853c033b019b4f9d23f598f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c31b73022883abac457b189621e589f0df26b28c7520d175f4f6e32dd2cd901f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "405491b473e9ae228417970c67c600da66eec8ae59c5097a46c5fe89c7159cec"}}}, "functions": {}, "sortedMiddleware": ["/"]}
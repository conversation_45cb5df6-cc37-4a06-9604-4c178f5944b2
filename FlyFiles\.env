
# MongoDB Configuration (used by NextAuth.js and the application)
MONGODB_URI=mongodb+srv://Myckas:<EMAIL>/flyfiles?retryWrites=true&w=majority&appName=Cluster0
GOOGLE_CLIENT_ID=980075175104-nm8tiq1ori3oq26blom6lnnvdj158n2j.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# NextAuth.js Configuration
NEXTAUTH_URL=http://localhost:3000  # For local development
NEXTAUTH_SECRET=C2owEEXvbdxBeVYuC6IrsoENYflIPld0xl78GXFPO1U # Added by `npx auth`. Read more: https://cli.authjs.dev

# Production URL (comment out for local development)
# NEXTAUTH_URL=https://flyfiles.mcdevhub.dk


# Cloudinary configuration (FOR FUTURE USE)
CLOUDINARY_CLOUD_NAME=dwqxk2tip
CLOUDINARY_API_KEY=***************
CLOUDINARY_API_SECRET=rZk3RfvtL1PxPUFT-HplZOKMVTo
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=dwqxk2tip



STRIPE_SECRET_KEY=sk_test_51OlcexGunuvAGRgKFWJL30XibiiWgJ5uPh6SjaRkbwdrSQc5x45LLLW6xUdRzYnBeCyGgLiX3YV74i9uxSIiioxf009GPM9AXe
STRIPE_PUBLISHABLE_KEY=pk_test_51OlcexGunuvAGRgKWPhMq7kylg8AysbnC727bNPKAXBCPUNqWiaRi06UGlItG3Ulba3zOmGK8nb7FRAHqNKHQHY6004eSBlM86
NEXT_PUBLIC_BASE_URL=https://flyfiles.mcdevhub.dk  # Update this for production FOR FUTURE USE

# GoFile.io API Configuration
GOFILE_ACCOUNT_ID=c9dffe78-3881-4a3f-9b65-eaf61ef2482a
GOFILE_ACCOUNT_TOKEN=BYVnXzkJvu4pdQ3GdwDBc3EAdWNz8BSG
GOFILE_API_BASE_URL=https://api.gofile.io
GOFILE_UPLOAD_URL=https://upload.gofile.io


import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { fileCode: string } }
) {
  try {
    const { fileCode } = params;

    if (!fileCode) {
      return NextResponse.json(
        { error: 'File code is required' },
        { status: 400 }
      );
    }

    const baseUrl = process.env.GOFILE_API_BASE_URL || 'https://api.gofile.io';
    const accountToken = process.env.GOFILE_ACCOUNT_TOKEN;

    // Build URL with optional token
    let url = `${baseUrl}/getFile/${fileCode}`;
    if (accountToken) {
      url += `?token=${accountToken}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to get file info', details: result },
        { status: response.status }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Get file info error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Plan configurations - separated from MongoDB to avoid client-side bundling issues
export const PLAN_CONFIGS = {
  guest: {
    name: '<PERSON>æ<PERSON>',
    uploadLimit: {
      amount: 250 * 1024 * 1024, // 250MB
      period: 'session' as const
    },
    fileExpiry: 7, // dage
    downloadLimits: {
      configurable: false,
      unlimited: true
    },
    features: [
      '250MB pr. session',
      '7 dages filudløb',
      'Ubegrænsede downloads',
      'Ingen konto nødvendig'
    ]
  },
  free: {
    name: '<PERSON><PERSON><PERSON> Konto',
    uploadLimit: {
      amount: 15 * 1024 * 1024 * 1024, // 15GB
      period: 'måned' as const
    },
    fileExpiry: 10, // dage
    downloadLimits: {
      configurable: true,
      unlimited: false
    },
    features: [
      '15GB månedlig upload',
      '10-dages filudløb',
      'Konfigurerbare downloadgrænser',
      'Google login påkrævet'
    ]
  },
  upgrade1: {
    name: 'Opgradering 1',
    uploadLimit: {
      amount: 15 * 1024 * 1024 * 1024, // 15GB
      period: 'uge' as const
    },
    fileExpiry: 14, // dage
    downloadLimits: {
      configurable: true,
      unlimited: false
    },
    features: [
      '15GB ugentlig upload',
      '14-dages filudløb',
      'Download statistik',
      'Alle Gratis funktioner'
    ],
    price: {
      amount: 5,
      period: 'måned' as const
    }
  },
  upgrade2: {
    name: 'Opgradering 2',
    uploadLimit: {
      amount: 50 * 1024 * 1024 * 1024, // 50GB
      period: 'uge' as const
    },
    fileExpiry: 30, // dage
    downloadLimits: {
      configurable: true,
      unlimited: true
    },
    features: [
      '50GB ugentlig upload',
      '30-dages filudløb',
      'Ubegrænsede downloads',
      'Avanceret analyse',
      'Alle tidligere funktioner'
    ],
    price: {
      amount: 25,
      period: 'måned' as const
    }
  }
} as const;

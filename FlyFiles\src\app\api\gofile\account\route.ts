import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const accountToken = process.env.GOFILE_ACCOUNT_TOKEN;
    const accountId = process.env.GOFILE_ACCOUNT_ID;

    if (!accountToken) {
      return NextResponse.json(
        { error: 'Account token not configured' },
        { status: 401 }
      );
    }

    if (!accountId) {
      return NextResponse.json(
        { error: 'Account ID not configured' },
        { status: 401 }
      );
    }

    // Get account details
    const response = await fetch(`https://api.gofile.io/accounts/${accountId}?token=${accountToken}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    let result;
    const responseText = await response.text();

    try {
      result = JSON.parse(responseText);
    } catch (error) {
      console.log('Raw response:', responseText);
      return NextResponse.json(
        { error: 'Invalid JSON response', rawResponse: responseText },
        { status: 500 }
      );
    }

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to get account details', details: result },
        { status: response.status }
      );
    }

    return NextResponse.json({
      accountId,
      accountToken: accountToken ? 'Present' : 'Missing',
      ...result
    });
  } catch (error) {
    console.error('Get account details error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

"use client"

import { useEffect } from "react"
import { useSession, signIn } from "next-auth/react"
import { useRouter } from "next/navigation"
import { Button } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Chrome, ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function LoginPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (session) {
      router.push('/dashboard')
    }
  }, [session, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (session) {
    return null // Will redirect to dashboard
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">F</span>
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900 dark:text-white">
            Log ind på FlyFiles
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Få adgang til 15GB månedlig upload og længere fil-opbevaring
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Vælg login metode</CardTitle>
            <CardDescription>
              Vi bruger kun Google OAuth for sikker og nem adgang
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={() => signIn('google')}
              className="w-full flex items-center justify-center space-x-2"
              size="lg"
            >
              <img 
                src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/3c/Google_Favicon_2025.svg/120px-Google_Favicon_2025.svg.png" 
                alt="Google logo" 
                className="h-5 w-5"
              />
              <span>Fortsæt med Google</span>
            </Button>

            <div className="text-center text-sm text-gray-500 dark:text-gray-400">
              <p>Ved at logge ind accepterer du vores</p>
              <p>
                <Link href="/terms" className="text-blue-600 hover:underline">
                  Servicevilkår
                </Link>
                {" og "}
                <Link href="/privacy" className="text-blue-600 hover:underline">
                  Privatlivspolitik
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="text-center">
          <Link 
            href="/"
            className="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Tilbage til forsiden
          </Link>
        </div>

        <Card>
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">Fordele ved at logge ind:</h3>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• 15GB månedlig upload (vs. 250MB som gæst)</li>
              <li>• 10 dages fil-opbevaring (vs. 7 dage)</li>
              <li>• Konfigurerbare download-grænser</li>
              <li>• Filhistorik og statistikker</li>
              <li>• Mulighed for upgrade til større planer</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 
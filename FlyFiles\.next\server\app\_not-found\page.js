/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=C%3A%5CUsers%5CMicka%5CCascadeProjects%5CFlyFiles%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cbuiltin%5Cglobal-not-found.js&appDir=C%3A%5CUsers%5CMicka%5CCascadeProjects%5CFlyFiles%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMicka%5CCascadeProjects%5CFlyFiles&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=C%3A%5CUsers%5CMicka%5CCascadeProjects%5CFlyFiles%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cbuiltin%5Cglobal-not-found.js&appDir=C%3A%5CUsers%5CMicka%5CCascadeProjects%5CFlyFiles%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMicka%5CCascadeProjects%5CFlyFiles&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n              children: [\"/_not-found\", {\n                children: ['__PAGE__', {}, {\n                  page: [\n                    notFound0,\n                    \"next/dist/client/components/builtin/not-found.js\"\n                  ]\n                }]\n              }, {}]\n            },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module2, \"next/dist/client/components/builtin/global-error.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/_not-found/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZfbm90LWZvdW5kJTJGcGFnZSZwYWdlPSUyRl9ub3QtZm91bmQlMkZwYWdlJmFwcFBhdGhzPSZwYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUNNaWNrYSU1Q0Nhc2NhZGVQcm9qZWN0cyU1Q0ZseUZpbGVzJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDYnVpbHRpbiU1Q2dsb2JhbC1ub3QtZm91bmQuanMmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q01pY2thJTVDQ2FzY2FkZVByb2plY3RzJTVDRmx5RmlsZXMlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q01pY2thJTVDQ2FzY2FkZVByb2plY3RzJTVDRmx5RmlsZXMmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QmaXNHbG9iYWxOb3RGb3VuZEVuYWJsZWQ9ISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSx3QkFBd0IsaU9BQXFGO0FBQzdHLHNCQUFzQixvSkFBc0c7QUFDNUgsc0JBQXNCLHVPQUF3RjtBQUM5RyxzQkFBc0IsaU9BQXFGO0FBQzNHLHNCQUFzQix1T0FBd0Y7QUFHNUc7QUFHQTtBQUMyRTtBQUNMO0FBQ1Q7QUFDTztBQUNPO0FBQ087QUFDUDtBQUNLO0FBQ1k7QUFDVztBQUN4QjtBQUNGO0FBQ2E7QUFDaUU7QUFDaEY7QUFDWDtBQUNRO0FBQ2hCO0FBQ3VCO0FBQ1A7QUFDVDtBQUNpQjtBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixlQUFlLElBQUk7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBOFA7QUFDbFM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDcUI7QUFDdkIsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBR0U7QUFDb0Y7QUFHcEY7QUFDRjtBQUNPLHdCQUF3Qix1R0FBa0I7QUFDakQ7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMLGFBQWEsT0FBb0MsSUFBSSxDQUFFO0FBQ3ZELGdCQUFnQixNQUF1QztBQUN2RCxDQUFDO0FBQ007QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxLQUFxQixFQUFFLEVBRTFCLENBQUM7QUFDTjtBQUNBO0FBQ0E7QUFDQSwrQkFBK0IsT0FBd0M7QUFDdkUsNkJBQTZCLDZFQUFjO0FBQzNDO0FBQ0Esd0JBQXdCLDZFQUFjO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHFTQUFxUztBQUNqVDtBQUNBLDhCQUE4Qiw4RkFBZ0I7QUFDOUMsVUFBVSx1QkFBdUI7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IscUZBQVU7QUFDOUIsc0JBQXNCLDBGQUFnQjtBQUN0QztBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsNkVBQWMscURBQXFELHdHQUEyQjtBQUNqSTtBQUNBLHlCQUF5Qiw2RUFBYyw2Q0FBNkMsdUZBQVU7QUFDOUYsbUNBQW1DLDJHQUF5QjtBQUM1RDtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsMkZBQW9CO0FBQ2xEO0FBQ0E7QUFDQSxxQ0FBcUMsTUFBNEcsSUFBSSxDQUFlO0FBQ3BLO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0Esa0NBQWtDLDZFQUFjO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELHNHQUE0QjtBQUNqRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxvRUFBUztBQUNwQjtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDZHQUE4QjtBQUN0QztBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsZ0dBQXFCO0FBQ2xEO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0EsbUJBQW1CLDRFQUFTO0FBQzVCO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyw0RUFBZTtBQUMvQyxnQ0FBZ0MsNkVBQWdCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixJQUFzQztBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBaUUsZ0ZBQWM7QUFDL0UsK0RBQStELHlDQUF5QztBQUN4RztBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxRQUFRLEVBQUUsTUFBTTtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLGtCQUFrQjtBQUNsQix1Q0FBdUMsUUFBUSxFQUFFLFFBQVE7QUFDekQ7QUFDQSxhQUFhO0FBQ2I7QUFDQSxrQ0FBa0Msc0NBQXNDO0FBQ3hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQiwwQ0FBMEMsNkVBQWM7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQSwrQkFBK0IsMkZBQWM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsNkVBQWM7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLElBQUk7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLDRDQUE0QztBQUM1QztBQUNBLHlCQUF5Qiw2RUFBYztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixXQUFXO0FBQy9CLG9CQUFvQiwwQkFBMEI7QUFDOUMsbUNBQW1DO0FBQ25DO0FBQ0Esd0JBQXdCLDRFQUFzQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4R0FBOEcsaUJBQWlCLEVBQUUsb0ZBQW9GLDhCQUE4QixPQUFPO0FBQzFQO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDZFQUFlO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsdURBQXVEO0FBQ2xHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLDJFQUFrQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxpRUFBWSxjQUFjLGdGQUFLO0FBQ2hFLCtCQUErQixpRUFBWTtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBEQUEwRCxpRUFBWTtBQUN0RSwrQkFBK0IsaUVBQVk7QUFDM0M7QUFDQSxpREFBaUQsaUVBQVk7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxpRUFBWTtBQUM3Qyw4QkFBOEIsNkZBQWU7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLGtFQUFTO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1RUFBdUUsZ0dBQXNCO0FBQzdGLDZCQUE2QjtBQUM3QjtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSw4QkFBOEIsNkVBQWU7QUFDN0MsOEJBQThCLHVFQUFZO0FBQzFDLG9DQUFvQztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0VBQStFLDZFQUFjLHdEQUF3RCxnR0FBc0I7QUFDM0s7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLDJCQUEyQixrRUFBUztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLHVHQUF1Ryw2RUFBZTtBQUN0SDtBQUNBLGlIQUFpSCxtRkFBbUY7QUFDcE07QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHFHQUF3QjtBQUN0RDtBQUNBLG9CQUFvQixvQkFBb0I7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdIQUFnSCxvQ0FBb0M7QUFDcEo7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQSx3Q0FBd0Msb0VBQWM7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUhBQWlILDZFQUFlO0FBQ2hJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHFHQUF3QjtBQUN0RDtBQUNBO0FBQ0EsaUhBQWlILDRFQUFzQjtBQUN2STtBQUNBLGtDQUFrQyw0RUFBc0I7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsZ0ZBQWdCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsdUVBQVk7QUFDNUM7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixnRkFBZ0I7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qix1RUFBWTtBQUN4QztBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsNkVBQWM7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLHlCQUF5Qiw2RUFBYztBQUN2QyxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyw0RUFBc0I7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyR0FBMkcsNEVBQXNCO0FBQ2pJO0FBQ0EsOEJBQThCLDRFQUFzQjtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELGlHQUFrQjtBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixxR0FBd0I7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQSwyQkFBMkIsZ0ZBQWdCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixnRkFBZ0I7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qix1RUFBWTtBQUN4QztBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixnRkFBZ0I7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMscUZBQVk7QUFDdkQ7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQix1QkFBdUIsZ0ZBQWdCO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSwrRkFBK0YsNkVBQWU7QUFDOUc7QUFDQSxzR0FBc0csdUVBQXVFO0FBQzdLO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiLG1CQUFtQixnRkFBZ0I7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVixvRkFBb0YsZ0ZBQWM7QUFDbEcsaUNBQWlDLFFBQVEsRUFBRSxRQUFRO0FBQ25ELDBCQUEwQix1RUFBUTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLE1BQU07QUFDTjtBQUNBLDRDQUE0Qyw2RkFBZTtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQywyRkFBbUI7QUFDckQ7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG5vdEZvdW5kMCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2J1aWx0aW4vbm90LWZvdW5kLmpzXCIpO1xuY29uc3QgbW9kdWxlMSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTWlja2FcXFxcQ2FzY2FkZVByb2plY3RzXFxcXEZseUZpbGVzXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbmNvbnN0IG1vZHVsZTIgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9idWlsdGluL2dsb2JhbC1lcnJvci5qc1wiKTtcbmNvbnN0IG1vZHVsZTMgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9idWlsdGluL2ZvcmJpZGRlbi5qc1wiKTtcbmNvbnN0IG1vZHVsZTQgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9idWlsdGluL3VuYXV0aG9yaXplZC5qc1wiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5pbXBvcnQgeyBnZXRSZXZhbGlkYXRlUmVhc29uIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvaW5zdHJ1bWVudGF0aW9uL3V0aWxzXCI7XG5pbXBvcnQgeyBnZXRUcmFjZXIsIFNwYW5LaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3RyYWNlL3RyYWNlclwiO1xuaW1wb3J0IHsgZ2V0UmVxdWVzdE1ldGEgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yZXF1ZXN0LW1ldGFcIjtcbmltcG9ydCB7IEJhc2VTZXJ2ZXJTcGFuIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3RyYWNlL2NvbnN0YW50c1wiO1xuaW1wb3J0IHsgaW50ZXJvcERlZmF1bHQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2ludGVyb3AtZGVmYXVsdFwiO1xuaW1wb3J0IHsgTm9kZU5leHRSZXF1ZXN0LCBOb2RlTmV4dFJlc3BvbnNlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYmFzZS1odHRwL25vZGVcIjtcbmltcG9ydCB7IGNoZWNrSXNBcHBQUFJFbmFibGVkIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL2V4cGVyaW1lbnRhbC9wcHJcIjtcbmltcG9ydCB7IGdldEZhbGxiYWNrUm91dGVQYXJhbXMgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yZXF1ZXN0L2ZhbGxiYWNrLXBhcmFtc1wiO1xuaW1wb3J0IHsgc2V0UmVmZXJlbmNlTWFuaWZlc3RzU2luZ2xldG9uIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbmNyeXB0aW9uLXV0aWxzXCI7XG5pbXBvcnQgeyBpc0h0bWxCb3RSZXF1ZXN0LCBzaG91bGRTZXJ2ZVN0cmVhbWluZ01ldGFkYXRhIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3N0cmVhbWluZy1tZXRhZGF0YVwiO1xuaW1wb3J0IHsgY3JlYXRlU2VydmVyTW9kdWxlTWFwIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9hY3Rpb24tdXRpbHNcIjtcbmltcG9ydCB7IG5vcm1hbGl6ZUFwcFBhdGggfSBmcm9tIFwibmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2FwcC1wYXRoc1wiO1xuaW1wb3J0IHsgZ2V0SXNQb3NzaWJsZVNlcnZlckFjdGlvbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9zZXJ2ZXItYWN0aW9uLXJlcXVlc3QtbWV0YVwiO1xuaW1wb3J0IHsgUlNDX0hFQURFUiwgTkVYVF9ST1VURVJfUFJFRkVUQ0hfSEVBREVSLCBORVhUX0lTX1BSRVJFTkRFUl9IRUFERVIsIE5FWFRfRElEX1BPU1RQT05FX0hFQURFUiB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci1oZWFkZXJzXCI7XG5pbXBvcnQgeyBnZXRCb3RUeXBlLCBpc0JvdCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtYm90XCI7XG5pbXBvcnQgeyBDYWNoZWRSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yZXNwb25zZS1jYWNoZVwiO1xuaW1wb3J0IHsgRmFsbGJhY2tNb2RlLCBwYXJzZUZhbGxiYWNrRmllbGQgfSBmcm9tIFwibmV4dC9kaXN0L2xpYi9mYWxsYmFja1wiO1xuaW1wb3J0IFJlbmRlclJlc3VsdCBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yZW5kZXItcmVzdWx0XCI7XG5pbXBvcnQgeyBDQUNIRV9PTkVfWUVBUiwgTkVYVF9DQUNIRV9UQUdTX0hFQURFUiB9IGZyb20gXCJuZXh0L2Rpc3QvbGliL2NvbnN0YW50c1wiO1xuaW1wb3J0IHsgRU5DT0RFRF9UQUdTIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvc3RyZWFtLXV0aWxzL2VuY29kZWQtdGFnc1wiO1xuaW1wb3J0IHsgc2VuZFJlbmRlclJlc3VsdCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3NlbmQtcGF5bG9hZFwiO1xuaW1wb3J0IHsgTm9GYWxsYmFja0Vycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9zaGFyZWQvbGliL25vLWZhbGxiYWNrLWVycm9yLmV4dGVybmFsXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICAgICAgICBjaGlsZHJlbjogW1wiL19ub3QtZm91bmRcIiwge1xuICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICAgICAgICAgIHBhZ2U6IFtcbiAgICAgICAgICAgICAgICAgICAgbm90Rm91bmQwLFxuICAgICAgICAgICAgICAgICAgICBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9idWlsdGluL25vdC1mb3VuZC5qc1wiXG4gICAgICAgICAgICAgICAgICBdXG4gICAgICAgICAgICAgICAgfV1cbiAgICAgICAgICAgICAgfSwge31dXG4gICAgICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbbW9kdWxlMSwgXCJDOlxcXFxVc2Vyc1xcXFxNaWNrYVxcXFxDYXNjYWRlUHJvamVjdHNcXFxcRmx5RmlsZXNcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ2dsb2JhbC1lcnJvcic6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9idWlsdGluL2dsb2JhbC1lcnJvci5qc1wiXSxcbidmb3JiaWRkZW4nOiBbbW9kdWxlMywgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9mb3JiaWRkZW4uanNcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTQsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2J1aWx0aW4vdW5hdXRob3JpemVkLmpzXCJdLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhQzpcXFxcVXNlcnNcXFxcTWlja2FcXFxcQ2FzY2FkZVByb2plY3RzXFxcXEZseUZpbGVzXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5pbXBvcnQgR2xvYmFsRXJyb3IgZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9idWlsdGluL2dsb2JhbC1lcnJvci5qc1wiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbmV4cG9ydCB7IEdsb2JhbEVycm9yIH07XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmltcG9ydCAqIGFzIGVudHJ5QmFzZSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5pbXBvcnQgeyBSZWRpcmVjdFN0YXR1c0NvZGUgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlZGlyZWN0LXN0YXR1cy1jb2RlXCI7XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9fbm90LWZvdW5kL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL19ub3QtZm91bmRcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9LFxuICAgIGRpc3REaXI6IHByb2Nlc3MuZW52Ll9fTkVYVF9SRUxBVElWRV9ESVNUX0RJUiB8fCAnJyxcbiAgICBwcm9qZWN0RGlyOiBwcm9jZXNzLmVudi5fX05FWFRfUkVMQVRJVkVfUFJPSkVDVF9ESVIgfHwgJydcbn0pO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGhhbmRsZXIocmVxLCByZXMsIGN0eCkge1xuICAgIHZhciBfdGhpcztcbiAgICBsZXQgc3JjUGFnZSA9IFwiL19ub3QtZm91bmQvcGFnZVwiO1xuICAgIC8vIHR1cmJvcGFjayBkb2Vzbid0IG5vcm1hbGl6ZSBgL2luZGV4YCBpbiB0aGUgcGFnZSBuYW1lXG4gICAgLy8gc28gd2UgbmVlZCB0byB0byBwcm9jZXNzIGR5bmFtaWMgcm91dGVzIHByb3Blcmx5XG4gICAgLy8gVE9ETzogZml4IHR1cmJvcGFjayBwcm92aWRpbmcgZGlmZmVyaW5nIHZhbHVlIGZyb20gd2VicGFja1xuICAgIGlmIChwcm9jZXNzLmVudi5UVVJCT1BBQ0spIHtcbiAgICAgICAgc3JjUGFnZSA9IHNyY1BhZ2UucmVwbGFjZSgvXFwvaW5kZXgkLywgJycpIHx8ICcvJztcbiAgICB9IGVsc2UgaWYgKHNyY1BhZ2UgPT09ICcvaW5kZXgnKSB7XG4gICAgICAgIC8vIHdlIGFsd2F5cyBub3JtYWxpemUgL2luZGV4IHNwZWNpZmljYWxseVxuICAgICAgICBzcmNQYWdlID0gJy8nO1xuICAgIH1cbiAgICBjb25zdCBtdWx0aVpvbmVEcmFmdE1vZGUgPSBwcm9jZXNzLmVudi5fX05FWFRfTVVMVElfWk9ORV9EUkFGVF9NT0RFO1xuICAgIGNvbnN0IGluaXRpYWxQb3N0cG9uZWQgPSBnZXRSZXF1ZXN0TWV0YShyZXEsICdwb3N0cG9uZWQnKTtcbiAgICAvLyBUT0RPOiByZXBsYWNlIHdpdGggbW9yZSBzcGVjaWZpYyBmbGFnc1xuICAgIGNvbnN0IG1pbmltYWxNb2RlID0gZ2V0UmVxdWVzdE1ldGEocmVxLCAnbWluaW1hbE1vZGUnKTtcbiAgICBjb25zdCBwcmVwYXJlUmVzdWx0ID0gYXdhaXQgcm91dGVNb2R1bGUucHJlcGFyZShyZXEsIHJlcywge1xuICAgICAgICBzcmNQYWdlLFxuICAgICAgICBtdWx0aVpvbmVEcmFmdE1vZGVcbiAgICB9KTtcbiAgICBpZiAoIXByZXBhcmVSZXN1bHQpIHtcbiAgICAgICAgcmVzLnN0YXR1c0NvZGUgPSA0MDA7XG4gICAgICAgIHJlcy5lbmQoJ0JhZCBSZXF1ZXN0Jyk7XG4gICAgICAgIGN0eC53YWl0VW50aWwgPT0gbnVsbCA/IHZvaWQgMCA6IGN0eC53YWl0VW50aWwuY2FsbChjdHgsIFByb21pc2UucmVzb2x2ZSgpKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIGNvbnN0IHsgYnVpbGRJZCwgcXVlcnksIHBhcmFtcywgcGFyc2VkVXJsLCBwYWdlSXNEeW5hbWljLCBidWlsZE1hbmlmZXN0LCBuZXh0Rm9udE1hbmlmZXN0LCByZWFjdExvYWRhYmxlTWFuaWZlc3QsIHNlcnZlckFjdGlvbnNNYW5pZmVzdCwgY2xpZW50UmVmZXJlbmNlTWFuaWZlc3QsIHN1YnJlc291cmNlSW50ZWdyaXR5TWFuaWZlc3QsIHByZXJlbmRlck1hbmlmZXN0LCBpc0RyYWZ0TW9kZSwgcmVzb2x2ZWRQYXRobmFtZSwgcmV2YWxpZGF0ZU9ubHlHZW5lcmF0ZWQsIHJvdXRlclNlcnZlckNvbnRleHQsIG5leHRDb25maWcgfSA9IHByZXBhcmVSZXN1bHQ7XG4gICAgY29uc3QgcGF0aG5hbWUgPSBwYXJzZWRVcmwucGF0aG5hbWUgfHwgJy8nO1xuICAgIGNvbnN0IG5vcm1hbGl6ZWRTcmNQYWdlID0gbm9ybWFsaXplQXBwUGF0aChzcmNQYWdlKTtcbiAgICBsZXQgeyBpc09uRGVtYW5kUmV2YWxpZGF0ZSB9ID0gcHJlcGFyZVJlc3VsdDtcbiAgICBjb25zdCBwcmVyZW5kZXJJbmZvID0gcHJlcmVuZGVyTWFuaWZlc3QuZHluYW1pY1JvdXRlc1tub3JtYWxpemVkU3JjUGFnZV07XG4gICAgY29uc3QgaXNQcmVyZW5kZXJlZCA9IHByZXJlbmRlck1hbmlmZXN0LnJvdXRlc1tyZXNvbHZlZFBhdGhuYW1lXTtcbiAgICBsZXQgaXNTU0cgPSBCb29sZWFuKHByZXJlbmRlckluZm8gfHwgaXNQcmVyZW5kZXJlZCB8fCBwcmVyZW5kZXJNYW5pZmVzdC5yb3V0ZXNbbm9ybWFsaXplZFNyY1BhZ2VdKTtcbiAgICBjb25zdCB1c2VyQWdlbnQgPSByZXEuaGVhZGVyc1sndXNlci1hZ2VudCddIHx8ICcnO1xuICAgIGNvbnN0IGJvdFR5cGUgPSBnZXRCb3RUeXBlKHVzZXJBZ2VudCk7XG4gICAgY29uc3QgaXNIdG1sQm90ID0gaXNIdG1sQm90UmVxdWVzdChyZXEpO1xuICAgIC8qKlxuICAgKiBJZiB0cnVlLCB0aGlzIGluZGljYXRlcyB0aGF0IHRoZSByZXF1ZXN0IGJlaW5nIG1hZGUgaXMgZm9yIGFuIGFwcFxuICAgKiBwcmVmZXRjaCByZXF1ZXN0LlxuICAgKi8gY29uc3QgaXNQcmVmZXRjaFJTQ1JlcXVlc3QgPSBnZXRSZXF1ZXN0TWV0YShyZXEsICdpc1ByZWZldGNoUlNDUmVxdWVzdCcpID8/IEJvb2xlYW4ocmVxLmhlYWRlcnNbTkVYVF9ST1VURVJfUFJFRkVUQ0hfSEVBREVSXSk7XG4gICAgLy8gTk9URTogRG9uJ3QgZGVsZXRlIGhlYWRlcnNbUlNDXSB5ZXQsIGl0IHN0aWxsIG5lZWRzIHRvIGJlIHVzZWQgaW4gcmVuZGVyVG9IVE1MIGxhdGVyXG4gICAgY29uc3QgaXNSU0NSZXF1ZXN0ID0gZ2V0UmVxdWVzdE1ldGEocmVxLCAnaXNSU0NSZXF1ZXN0JykgPz8gQm9vbGVhbihyZXEuaGVhZGVyc1tSU0NfSEVBREVSXSk7XG4gICAgY29uc3QgaXNQb3NzaWJsZVNlcnZlckFjdGlvbiA9IGdldElzUG9zc2libGVTZXJ2ZXJBY3Rpb24ocmVxKTtcbiAgICAvKipcbiAgICogSWYgdGhlIHJvdXRlIGJlaW5nIHJlbmRlcmVkIGlzIGFuIGFwcCBwYWdlLCBhbmQgdGhlIHBwciBmZWF0dXJlIGhhcyBiZWVuXG4gICAqIGVuYWJsZWQsIHRoZW4gdGhlIGdpdmVuIHJvdXRlIF9jb3VsZF8gc3VwcG9ydCBQUFIuXG4gICAqLyBjb25zdCBjb3VsZFN1cHBvcnRQUFIgPSBjaGVja0lzQXBwUFBSRW5hYmxlZChuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5wcHIpO1xuICAgIC8vIFdoZW4gZW5hYmxlZCwgdGhpcyB3aWxsIGFsbG93IHRoZSB1c2Ugb2YgdGhlIGA/X19uZXh0cHByb25seWAgcXVlcnkgdG9cbiAgICAvLyBlbmFibGUgZGVidWdnaW5nIG9mIHRoZSBzdGF0aWMgc2hlbGwuXG4gICAgY29uc3QgaGFzRGVidWdTdGF0aWNTaGVsbFF1ZXJ5ID0gcHJvY2Vzcy5lbnYuX19ORVhUX0VYUEVSSU1FTlRBTF9TVEFUSUNfU0hFTExfREVCVUdHSU5HID09PSAnMScgJiYgdHlwZW9mIHF1ZXJ5Ll9fbmV4dHBwcm9ubHkgIT09ICd1bmRlZmluZWQnICYmIGNvdWxkU3VwcG9ydFBQUjtcbiAgICAvLyBXaGVuIGVuYWJsZWQsIHRoaXMgd2lsbCBhbGxvdyB0aGUgdXNlIG9mIHRoZSBgP19fbmV4dHBwcm9ubHlgIHF1ZXJ5XG4gICAgLy8gdG8gZW5hYmxlIGRlYnVnZ2luZyBvZiB0aGUgZmFsbGJhY2sgc2hlbGwuXG4gICAgY29uc3QgaGFzRGVidWdGYWxsYmFja1NoZWxsUXVlcnkgPSBoYXNEZWJ1Z1N0YXRpY1NoZWxsUXVlcnkgJiYgcXVlcnkuX19uZXh0cHByb25seSA9PT0gJ2ZhbGxiYWNrJztcbiAgICAvLyBUaGlzIHBhZ2Ugc3VwcG9ydHMgUFBSIGlmIGl0IGlzIG1hcmtlZCBhcyBiZWluZyBgUEFSVElBTExZX1NUQVRJQ2AgaW4gdGhlXG4gICAgLy8gcHJlcmVuZGVyIG1hbmlmZXN0IGFuZCB0aGlzIGlzIGFuIGFwcCBwYWdlLlxuICAgIGNvbnN0IGlzUm91dGVQUFJFbmFibGVkID0gY291bGRTdXBwb3J0UFBSICYmICgoKF90aGlzID0gcHJlcmVuZGVyTWFuaWZlc3Qucm91dGVzW25vcm1hbGl6ZWRTcmNQYWdlXSA/PyBwcmVyZW5kZXJNYW5pZmVzdC5keW5hbWljUm91dGVzW25vcm1hbGl6ZWRTcmNQYWdlXSkgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzLnJlbmRlcmluZ01vZGUpID09PSAnUEFSVElBTExZX1NUQVRJQycgfHwgLy8gSWRlYWxseSB3ZSdkIHdhbnQgdG8gY2hlY2sgdGhlIGFwcENvbmZpZyB0byBzZWUgaWYgdGhpcyBwYWdlIGhhcyBQUFJcbiAgICAvLyBlbmFibGVkIG9yIG5vdCwgYnV0IHRoYXQgd291bGQgcmVxdWlyZSBwbHVtYmluZyB0aGUgYXBwQ29uZmlnIHRocm91Z2hcbiAgICAvLyB0byB0aGUgc2VydmVyIGR1cmluZyBkZXZlbG9wbWVudC4gV2UgYXNzdW1lIHRoYXQgdGhlIHBhZ2Ugc3VwcG9ydHMgaXRcbiAgICAvLyBidXQgb25seSBkdXJpbmcgZGV2ZWxvcG1lbnQuXG4gICAgaGFzRGVidWdTdGF0aWNTaGVsbFF1ZXJ5ICYmIChyb3V0ZU1vZHVsZS5pc0RldiA9PT0gdHJ1ZSB8fCAocm91dGVyU2VydmVyQ29udGV4dCA9PSBudWxsID8gdm9pZCAwIDogcm91dGVyU2VydmVyQ29udGV4dC5leHBlcmltZW50YWxUZXN0UHJveHkpID09PSB0cnVlKSk7XG4gICAgY29uc3QgaXNEZWJ1Z1N0YXRpY1NoZWxsID0gaGFzRGVidWdTdGF0aWNTaGVsbFF1ZXJ5ICYmIGlzUm91dGVQUFJFbmFibGVkO1xuICAgIC8vIFdlIHNob3VsZCBlbmFibGUgZGVidWdnaW5nIGR5bmFtaWMgYWNjZXNzZXMgd2hlbiB0aGUgc3RhdGljIHNoZWxsXG4gICAgLy8gZGVidWdnaW5nIGhhcyBiZWVuIGVuYWJsZWQgYW5kIHdlJ3JlIGFsc28gaW4gZGV2ZWxvcG1lbnQgbW9kZS5cbiAgICBjb25zdCBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzID0gaXNEZWJ1Z1N0YXRpY1NoZWxsICYmIHJvdXRlTW9kdWxlLmlzRGV2ID09PSB0cnVlO1xuICAgIGNvbnN0IGlzRGVidWdGYWxsYmFja1NoZWxsID0gaGFzRGVidWdGYWxsYmFja1NoZWxsUXVlcnkgJiYgaXNSb3V0ZVBQUkVuYWJsZWQ7XG4gICAgLy8gSWYgd2UncmUgaW4gbWluaW1hbCBtb2RlLCB0aGVuIHRyeSB0byBnZXQgdGhlIHBvc3Rwb25lZCBpbmZvcm1hdGlvbiBmcm9tXG4gICAgLy8gdGhlIHJlcXVlc3QgbWV0YWRhdGEuIElmIGF2YWlsYWJsZSwgdXNlIGl0IGZvciByZXN1bWluZyB0aGUgcG9zdHBvbmVkXG4gICAgLy8gcmVuZGVyLlxuICAgIGNvbnN0IG1pbmltYWxQb3N0cG9uZWQgPSBpc1JvdXRlUFBSRW5hYmxlZCA/IGluaXRpYWxQb3N0cG9uZWQgOiB1bmRlZmluZWQ7XG4gICAgLy8gSWYgUFBSIGlzIGVuYWJsZWQsIGFuZCB0aGlzIGlzIGEgUlNDIHJlcXVlc3QgKGJ1dCBub3QgYSBwcmVmZXRjaCksIHRoZW5cbiAgICAvLyB3ZSBjYW4gdXNlIHRoaXMgZmFjdCB0byBvbmx5IGdlbmVyYXRlIHRoZSBmbGlnaHQgZGF0YSBmb3IgdGhlIHJlcXVlc3RcbiAgICAvLyBiZWNhdXNlIHdlIGNhbid0IGNhY2hlIHRoZSBIVE1MIChhcyBpdCdzIGFsc28gZHluYW1pYykuXG4gICAgY29uc3QgaXNEeW5hbWljUlNDUmVxdWVzdCA9IGlzUm91dGVQUFJFbmFibGVkICYmIGlzUlNDUmVxdWVzdCAmJiAhaXNQcmVmZXRjaFJTQ1JlcXVlc3Q7XG4gICAgLy8gTmVlZCB0byByZWFkIHRoaXMgYmVmb3JlIGl0J3Mgc3RyaXBwZWQgYnkgc3RyaXBGbGlnaHRIZWFkZXJzLiBXZSBkb24ndFxuICAgIC8vIG5lZWQgdG8gdHJhbnNmZXIgaXQgdG8gdGhlIHJlcXVlc3QgbWV0YSBiZWNhdXNlIGl0J3Mgb25seSByZWFkXG4gICAgLy8gd2l0aGluIHRoaXMgZnVuY3Rpb247IHRoZSBzdGF0aWMgc2VnbWVudCBkYXRhIHNob3VsZCBoYXZlIGFscmVhZHkgYmVlblxuICAgIC8vIGdlbmVyYXRlZCwgc28gd2Ugd2lsbCBhbHdheXMgZWl0aGVyIHJldHVybiBhIHN0YXRpYyByZXNwb25zZSBvciBhIDQwNC5cbiAgICBjb25zdCBzZWdtZW50UHJlZmV0Y2hIZWFkZXIgPSBnZXRSZXF1ZXN0TWV0YShyZXEsICdzZWdtZW50UHJlZmV0Y2hSU0NSZXF1ZXN0Jyk7XG4gICAgLy8gVE9ETzogaW52ZXN0aWdhdGUgZXhpc3RpbmcgYnVnIHdpdGggc2hvdWxkU2VydmVTdHJlYW1pbmdNZXRhZGF0YSBhbHdheXNcbiAgICAvLyBiZWluZyB0cnVlIGZvciBhIHJldmFsaWRhdGUgZHVlIHRvIG1vZGlmeWluZyB0aGUgYmFzZS1zZXJ2ZXIgdGhpcy5yZW5kZXJPcHRzXG4gICAgLy8gd2hlbiBmaXhpbmcgdGhpcyB0byBjb3JyZWN0IGxvZ2ljIGl0IGNhdXNlcyBoeWRyYXRpb24gaXNzdWUgc2luY2Ugd2Ugc2V0XG4gICAgLy8gc2VydmVTdHJlYW1pbmdNZXRhZGF0YSB0byB0cnVlIGR1cmluZyBleHBvcnRcbiAgICBsZXQgc2VydmVTdHJlYW1pbmdNZXRhZGF0YSA9ICF1c2VyQWdlbnQgPyB0cnVlIDogc2hvdWxkU2VydmVTdHJlYW1pbmdNZXRhZGF0YSh1c2VyQWdlbnQsIG5leHRDb25maWcuaHRtbExpbWl0ZWRCb3RzKTtcbiAgICBpZiAoaXNIdG1sQm90ICYmIGlzUm91dGVQUFJFbmFibGVkKSB7XG4gICAgICAgIGlzU1NHID0gZmFsc2U7XG4gICAgICAgIHNlcnZlU3RyZWFtaW5nTWV0YWRhdGEgPSBmYWxzZTtcbiAgICB9XG4gICAgLy8gSW4gZGV2ZWxvcG1lbnQsIHdlIGFsd2F5cyB3YW50IHRvIGdlbmVyYXRlIGR5bmFtaWMgSFRNTC5cbiAgICBsZXQgc3VwcG9ydHNEeW5hbWljUmVzcG9uc2UgPSAvLyBJZiB3ZSdyZSBpbiBkZXZlbG9wbWVudCwgd2UgYWx3YXlzIHN1cHBvcnQgZHluYW1pYyBIVE1MLCB1bmxlc3MgaXQnc1xuICAgIC8vIGEgZGF0YSByZXF1ZXN0LCBpbiB3aGljaCBjYXNlIHdlIG9ubHkgcHJvZHVjZSBzdGF0aWMgSFRNTC5cbiAgICByb3V0ZU1vZHVsZS5pc0RldiA9PT0gdHJ1ZSB8fCAvLyBJZiB0aGlzIGlzIG5vdCBTU0cgb3IgZG9lcyBub3QgaGF2ZSBzdGF0aWMgcGF0aHMsIHRoZW4gaXQgc3VwcG9ydHNcbiAgICAvLyBkeW5hbWljIEhUTUwuXG4gICAgIWlzU1NHIHx8IC8vIElmIHRoaXMgcmVxdWVzdCBoYXMgcHJvdmlkZWQgcG9zdHBvbmVkIGRhdGEsIGl0IHN1cHBvcnRzIGR5bmFtaWNcbiAgICAvLyBIVE1MLlxuICAgIHR5cGVvZiBpbml0aWFsUG9zdHBvbmVkID09PSAnc3RyaW5nJyB8fCAvLyBJZiB0aGlzIGlzIGEgZHluYW1pYyBSU0MgcmVxdWVzdCwgdGhlbiB0aGlzIHJlbmRlciBzdXBwb3J0cyBkeW5hbWljXG4gICAgLy8gSFRNTCAoaXQncyBkeW5hbWljKS5cbiAgICBpc0R5bmFtaWNSU0NSZXF1ZXN0O1xuICAgIC8vIFdoZW4gaHRtbCBib3RzIHJlcXVlc3QgUFBSIHBhZ2UsIHBlcmZvcm0gdGhlIGZ1bGwgZHluYW1pYyByZW5kZXJpbmcuXG4gICAgY29uc3Qgc2hvdWxkV2FpdE9uQWxsUmVhZHkgPSBpc0h0bWxCb3QgJiYgaXNSb3V0ZVBQUkVuYWJsZWQ7XG4gICAgbGV0IHNzZ0NhY2hlS2V5ID0gbnVsbDtcbiAgICBpZiAoIWlzRHJhZnRNb2RlICYmIGlzU1NHICYmICFzdXBwb3J0c0R5bmFtaWNSZXNwb25zZSAmJiAhaXNQb3NzaWJsZVNlcnZlckFjdGlvbiAmJiAhbWluaW1hbFBvc3Rwb25lZCAmJiAhaXNEeW5hbWljUlNDUmVxdWVzdCkge1xuICAgICAgICBzc2dDYWNoZUtleSA9IHJlc29sdmVkUGF0aG5hbWU7XG4gICAgfVxuICAgIC8vIHRoZSBzdGF0aWNQYXRoS2V5IGRpZmZlcnMgZnJvbSBzc2dDYWNoZUtleSBzaW5jZVxuICAgIC8vIHNzZ0NhY2hlS2V5IGlzIG51bGwgaW4gZGV2IHNpbmNlIHdlJ3JlIGFsd2F5cyBpbiBcImR5bmFtaWNcIlxuICAgIC8vIG1vZGUgaW4gZGV2IHRvIGJ5cGFzcyB0aGUgY2FjaGUsIGJ1dCB3ZSBzdGlsbCBuZWVkIHRvIGhvbm9yXG4gICAgLy8gZHluYW1pY1BhcmFtcyA9IGZhbHNlIGluIGRldiBtb2RlXG4gICAgbGV0IHN0YXRpY1BhdGhLZXkgPSBzc2dDYWNoZUtleTtcbiAgICBpZiAoIXN0YXRpY1BhdGhLZXkgJiYgcm91dGVNb2R1bGUuaXNEZXYpIHtcbiAgICAgICAgc3RhdGljUGF0aEtleSA9IHJlc29sdmVkUGF0aG5hbWU7XG4gICAgfVxuICAgIGNvbnN0IENvbXBvbmVudE1vZCA9IHtcbiAgICAgICAgLi4uZW50cnlCYXNlLFxuICAgICAgICB0cmVlLFxuICAgICAgICBwYWdlcyxcbiAgICAgICAgR2xvYmFsRXJyb3IsXG4gICAgICAgIGhhbmRsZXIsXG4gICAgICAgIHJvdXRlTW9kdWxlLFxuICAgICAgICBfX25leHRfYXBwX19cbiAgICB9O1xuICAgIC8vIEJlZm9yZSByZW5kZXJpbmcgKHdoaWNoIGluaXRpYWxpemVzIGNvbXBvbmVudCB0cmVlIG1vZHVsZXMpLCB3ZSBoYXZlIHRvXG4gICAgLy8gc2V0IHRoZSByZWZlcmVuY2UgbWFuaWZlc3RzIHRvIG91ciBnbG9iYWwgc3RvcmUgc28gU2VydmVyIEFjdGlvbidzXG4gICAgLy8gZW5jcnlwdGlvbiB1dGlsIGNhbiBhY2Nlc3MgdG8gdGhlbSBhdCB0aGUgdG9wIGxldmVsIG9mIHRoZSBwYWdlIG1vZHVsZS5cbiAgICBpZiAoc2VydmVyQWN0aW9uc01hbmlmZXN0ICYmIGNsaWVudFJlZmVyZW5jZU1hbmlmZXN0KSB7XG4gICAgICAgIHNldFJlZmVyZW5jZU1hbmlmZXN0c1NpbmdsZXRvbih7XG4gICAgICAgICAgICBwYWdlOiBzcmNQYWdlLFxuICAgICAgICAgICAgY2xpZW50UmVmZXJlbmNlTWFuaWZlc3QsXG4gICAgICAgICAgICBzZXJ2ZXJBY3Rpb25zTWFuaWZlc3QsXG4gICAgICAgICAgICBzZXJ2ZXJNb2R1bGVNYXA6IGNyZWF0ZVNlcnZlck1vZHVsZU1hcCh7XG4gICAgICAgICAgICAgICAgc2VydmVyQWN0aW9uc01hbmlmZXN0XG4gICAgICAgICAgICB9KVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgY29uc3QgbWV0aG9kID0gcmVxLm1ldGhvZCB8fCAnR0VUJztcbiAgICBjb25zdCB0cmFjZXIgPSBnZXRUcmFjZXIoKTtcbiAgICBjb25zdCBhY3RpdmVTcGFuID0gdHJhY2VyLmdldEFjdGl2ZVNjb3BlU3BhbigpO1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGludm9rZVJvdXRlTW9kdWxlID0gYXN5bmMgKHNwYW4sIGNvbnRleHQpPT57XG4gICAgICAgICAgICBjb25zdCBuZXh0UmVxID0gbmV3IE5vZGVOZXh0UmVxdWVzdChyZXEpO1xuICAgICAgICAgICAgY29uc3QgbmV4dFJlcyA9IG5ldyBOb2RlTmV4dFJlc3BvbnNlKHJlcyk7XG4gICAgICAgICAgICAvLyBUT0RPOiBhZGFwdCBmb3IgcHV0dGluZyB0aGUgUkRDIGluc2lkZSB0aGUgcG9zdHBvbmVkIGRhdGFcbiAgICAgICAgICAgIC8vIElmIHdlJ3JlIGluIGRldiwgYW5kIHRoaXMgaXNuJ3QgYSBwcmVmZXRjaCBvciBhIHNlcnZlciBhY3Rpb24sXG4gICAgICAgICAgICAvLyB3ZSBzaG91bGQgc2VlZCB0aGUgcmVzdW1lIGRhdGEgY2FjaGUuXG4gICAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICAgICAgICAgICAgICBpZiAobmV4dENvbmZpZy5leHBlcmltZW50YWwuZHluYW1pY0lPICYmICFpc1ByZWZldGNoUlNDUmVxdWVzdCAmJiAhY29udGV4dC5yZW5kZXJPcHRzLmlzUG9zc2libGVTZXJ2ZXJBY3Rpb24pIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgd2FybXVwID0gYXdhaXQgcm91dGVNb2R1bGUud2FybXVwKG5leHRSZXEsIG5leHRSZXMsIGNvbnRleHQpO1xuICAgICAgICAgICAgICAgICAgICAvLyBJZiB0aGUgd2FybXVwIGlzIHN1Y2Nlc3NmdWwsIHdlIHNob3VsZCB1c2UgdGhlIHJlc3VtZSBkYXRhXG4gICAgICAgICAgICAgICAgICAgIC8vIGNhY2hlIGZyb20gdGhlIHdhcm11cC5cbiAgICAgICAgICAgICAgICAgICAgaWYgKHdhcm11cC5tZXRhZGF0YS5yZW5kZXJSZXN1bWVEYXRhQ2FjaGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRleHQucmVuZGVyT3B0cy5yZW5kZXJSZXN1bWVEYXRhQ2FjaGUgPSB3YXJtdXAubWV0YWRhdGEucmVuZGVyUmVzdW1lRGF0YUNhY2hlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHJvdXRlTW9kdWxlLnJlbmRlcihuZXh0UmVxLCBuZXh0UmVzLCBjb250ZXh0KS5maW5hbGx5KCgpPT57XG4gICAgICAgICAgICAgICAgaWYgKCFzcGFuKSByZXR1cm47XG4gICAgICAgICAgICAgICAgc3Bhbi5zZXRBdHRyaWJ1dGVzKHtcbiAgICAgICAgICAgICAgICAgICAgJ2h0dHAuc3RhdHVzX2NvZGUnOiByZXMuc3RhdHVzQ29kZSxcbiAgICAgICAgICAgICAgICAgICAgJ25leHQucnNjJzogZmFsc2VcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCByb290U3BhbkF0dHJpYnV0ZXMgPSB0cmFjZXIuZ2V0Um9vdFNwYW5BdHRyaWJ1dGVzKCk7XG4gICAgICAgICAgICAgICAgLy8gV2Ugd2VyZSB1bmFibGUgdG8gZ2V0IGF0dHJpYnV0ZXMsIHByb2JhYmx5IE9URUwgaXMgbm90IGVuYWJsZWRcbiAgICAgICAgICAgICAgICBpZiAoIXJvb3RTcGFuQXR0cmlidXRlcykge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChyb290U3BhbkF0dHJpYnV0ZXMuZ2V0KCduZXh0LnNwYW5fdHlwZScpICE9PSBCYXNlU2VydmVyU3Bhbi5oYW5kbGVSZXF1ZXN0KSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihgVW5leHBlY3RlZCByb290IHNwYW4gdHlwZSAnJHtyb290U3BhbkF0dHJpYnV0ZXMuZ2V0KCduZXh0LnNwYW5fdHlwZScpfScuIFBsZWFzZSByZXBvcnQgdGhpcyBOZXh0LmpzIGlzc3VlIGh0dHBzOi8vZ2l0aHViLmNvbS92ZXJjZWwvbmV4dC5qc2ApO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IHJvdXRlID0gcm9vdFNwYW5BdHRyaWJ1dGVzLmdldCgnbmV4dC5yb3V0ZScpO1xuICAgICAgICAgICAgICAgIGlmIChyb3V0ZSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBuYW1lID0gYCR7bWV0aG9kfSAke3JvdXRlfWA7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4uc2V0QXR0cmlidXRlcyh7XG4gICAgICAgICAgICAgICAgICAgICAgICAnbmV4dC5yb3V0ZSc6IHJvdXRlLFxuICAgICAgICAgICAgICAgICAgICAgICAgJ2h0dHAucm91dGUnOiByb3V0ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICduZXh0LnNwYW5fbmFtZSc6IG5hbWVcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4udXBkYXRlTmFtZShuYW1lKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBzcGFuLnVwZGF0ZU5hbWUoYCR7bWV0aG9kfSAke3JlcS51cmx9YCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IGRvUmVuZGVyID0gYXN5bmMgKHsgc3BhbiwgcG9zdHBvbmVkLCBmYWxsYmFja1JvdXRlUGFyYW1zIH0pPT57XG4gICAgICAgICAgICBjb25zdCBjb250ZXh0ID0ge1xuICAgICAgICAgICAgICAgIHF1ZXJ5LFxuICAgICAgICAgICAgICAgIHBhcmFtcyxcbiAgICAgICAgICAgICAgICBwYWdlOiBub3JtYWxpemVkU3JjUGFnZSxcbiAgICAgICAgICAgICAgICBzaGFyZWRDb250ZXh0OiB7XG4gICAgICAgICAgICAgICAgICAgIGJ1aWxkSWRcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIHNlcnZlckNvbXBvbmVudHNIbXJDYWNoZTogZ2V0UmVxdWVzdE1ldGEocmVxLCAnc2VydmVyQ29tcG9uZW50c0htckNhY2hlJyksXG4gICAgICAgICAgICAgICAgZmFsbGJhY2tSb3V0ZVBhcmFtcyxcbiAgICAgICAgICAgICAgICByZW5kZXJPcHRzOiB7XG4gICAgICAgICAgICAgICAgICAgIEFwcDogKCk9Pm51bGwsXG4gICAgICAgICAgICAgICAgICAgIERvY3VtZW50OiAoKT0+bnVsbCxcbiAgICAgICAgICAgICAgICAgICAgcGFnZUNvbmZpZzoge30sXG4gICAgICAgICAgICAgICAgICAgIENvbXBvbmVudE1vZCxcbiAgICAgICAgICAgICAgICAgICAgQ29tcG9uZW50OiBpbnRlcm9wRGVmYXVsdChDb21wb25lbnRNb2QpLFxuICAgICAgICAgICAgICAgICAgICBwYXJhbXMsXG4gICAgICAgICAgICAgICAgICAgIHJvdXRlTW9kdWxlLFxuICAgICAgICAgICAgICAgICAgICBwYWdlOiBzcmNQYWdlLFxuICAgICAgICAgICAgICAgICAgICBwb3N0cG9uZWQsXG4gICAgICAgICAgICAgICAgICAgIHNob3VsZFdhaXRPbkFsbFJlYWR5LFxuICAgICAgICAgICAgICAgICAgICBzZXJ2ZVN0cmVhbWluZ01ldGFkYXRhLFxuICAgICAgICAgICAgICAgICAgICBzdXBwb3J0c0R5bmFtaWNSZXNwb25zZTogdHlwZW9mIHBvc3Rwb25lZCA9PT0gJ3N0cmluZycgfHwgc3VwcG9ydHNEeW5hbWljUmVzcG9uc2UsXG4gICAgICAgICAgICAgICAgICAgIGJ1aWxkTWFuaWZlc3QsXG4gICAgICAgICAgICAgICAgICAgIG5leHRGb250TWFuaWZlc3QsXG4gICAgICAgICAgICAgICAgICAgIHJlYWN0TG9hZGFibGVNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICAgICAgc3VicmVzb3VyY2VJbnRlZ3JpdHlNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICAgICAgc2VydmVyQWN0aW9uc01hbmlmZXN0LFxuICAgICAgICAgICAgICAgICAgICBjbGllbnRSZWZlcmVuY2VNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICAgICAgc2V0SXNyU3RhdHVzOiByb3V0ZXJTZXJ2ZXJDb250ZXh0ID09IG51bGwgPyB2b2lkIDAgOiByb3V0ZXJTZXJ2ZXJDb250ZXh0LnNldElzclN0YXR1cyxcbiAgICAgICAgICAgICAgICAgICAgZGlyOiByb3V0ZU1vZHVsZS5wcm9qZWN0RGlyLFxuICAgICAgICAgICAgICAgICAgICBpc0RyYWZ0TW9kZSxcbiAgICAgICAgICAgICAgICAgICAgaXNSZXZhbGlkYXRlOiBpc1NTRyAmJiAhcG9zdHBvbmVkICYmICFpc0R5bmFtaWNSU0NSZXF1ZXN0LFxuICAgICAgICAgICAgICAgICAgICBib3RUeXBlLFxuICAgICAgICAgICAgICAgICAgICBpc09uRGVtYW5kUmV2YWxpZGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgaXNQb3NzaWJsZVNlcnZlckFjdGlvbixcbiAgICAgICAgICAgICAgICAgICAgYXNzZXRQcmVmaXg6IG5leHRDb25maWcuYXNzZXRQcmVmaXgsXG4gICAgICAgICAgICAgICAgICAgIG5leHRDb25maWdPdXRwdXQ6IG5leHRDb25maWcub3V0cHV0LFxuICAgICAgICAgICAgICAgICAgICBjcm9zc09yaWdpbjogbmV4dENvbmZpZy5jcm9zc09yaWdpbixcbiAgICAgICAgICAgICAgICAgICAgdHJhaWxpbmdTbGFzaDogbmV4dENvbmZpZy50cmFpbGluZ1NsYXNoLFxuICAgICAgICAgICAgICAgICAgICBwcmV2aWV3UHJvcHM6IHByZXJlbmRlck1hbmlmZXN0LnByZXZpZXcsXG4gICAgICAgICAgICAgICAgICAgIGRlcGxveW1lbnRJZDogbmV4dENvbmZpZy5kZXBsb3ltZW50SWQsXG4gICAgICAgICAgICAgICAgICAgIGVuYWJsZVRhaW50aW5nOiBuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC50YWludCxcbiAgICAgICAgICAgICAgICAgICAgaHRtbExpbWl0ZWRCb3RzOiBuZXh0Q29uZmlnLmh0bWxMaW1pdGVkQm90cyxcbiAgICAgICAgICAgICAgICAgICAgZGV2dG9vbFNlZ21lbnRFeHBsb3JlcjogbmV4dENvbmZpZy5leHBlcmltZW50YWwuZGV2dG9vbFNlZ21lbnRFeHBsb3JlcixcbiAgICAgICAgICAgICAgICAgICAgcmVhY3RNYXhIZWFkZXJzTGVuZ3RoOiBuZXh0Q29uZmlnLnJlYWN0TWF4SGVhZGVyc0xlbmd0aCxcbiAgICAgICAgICAgICAgICAgICAgbXVsdGlab25lRHJhZnRNb2RlLFxuICAgICAgICAgICAgICAgICAgICBpbmNyZW1lbnRhbENhY2hlOiBnZXRSZXF1ZXN0TWV0YShyZXEsICdpbmNyZW1lbnRhbENhY2hlJyksXG4gICAgICAgICAgICAgICAgICAgIGNhY2hlTGlmZVByb2ZpbGVzOiBuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5jYWNoZUxpZmUsXG4gICAgICAgICAgICAgICAgICAgIGJhc2VQYXRoOiBuZXh0Q29uZmlnLmJhc2VQYXRoLFxuICAgICAgICAgICAgICAgICAgICBzZXJ2ZXJBY3Rpb25zOiBuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5zZXJ2ZXJBY3Rpb25zLFxuICAgICAgICAgICAgICAgICAgICAuLi5pc0RlYnVnU3RhdGljU2hlbGwgfHwgaXNEZWJ1Z0R5bmFtaWNBY2Nlc3NlcyA/IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5leHRFeHBvcnQ6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBzdXBwb3J0c0R5bmFtaWNSZXNwb25zZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc1N0YXRpY0dlbmVyYXRpb246IHRydWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc1JldmFsaWRhdGU6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzOiBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzXG4gICAgICAgICAgICAgICAgICAgIH0gOiB7fSxcbiAgICAgICAgICAgICAgICAgICAgZXhwZXJpbWVudGFsOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpc1JvdXRlUFBSRW5hYmxlZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGV4cGlyZVRpbWU6IG5leHRDb25maWcuZXhwaXJlVGltZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0YWxlVGltZXM6IG5leHRDb25maWcuZXhwZXJpbWVudGFsLnN0YWxlVGltZXMsXG4gICAgICAgICAgICAgICAgICAgICAgICBkeW5hbWljSU86IEJvb2xlYW4obmV4dENvbmZpZy5leHBlcmltZW50YWwuZHluYW1pY0lPKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsaWVudFNlZ21lbnRDYWNoZTogQm9vbGVhbihuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5jbGllbnRTZWdtZW50Q2FjaGUpLFxuICAgICAgICAgICAgICAgICAgICAgICAgZHluYW1pY09uSG92ZXI6IEJvb2xlYW4obmV4dENvbmZpZy5leHBlcmltZW50YWwuZHluYW1pY09uSG92ZXIpLFxuICAgICAgICAgICAgICAgICAgICAgICAgaW5saW5lQ3NzOiBCb29sZWFuKG5leHRDb25maWcuZXhwZXJpbWVudGFsLmlubGluZUNzcyksXG4gICAgICAgICAgICAgICAgICAgICAgICBhdXRoSW50ZXJydXB0czogQm9vbGVhbihuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5hdXRoSW50ZXJydXB0cyksXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGllbnRUcmFjZU1ldGFkYXRhOiBuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5jbGllbnRUcmFjZU1ldGFkYXRhIHx8IFtdXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIHdhaXRVbnRpbDogY3R4LndhaXRVbnRpbCxcbiAgICAgICAgICAgICAgICAgICAgb25DbG9zZTogKGNiKT0+e1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVzLm9uKCdjbG9zZScsIGNiKTtcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgb25BZnRlclRhc2tFcnJvcjogKCk9Pnt9LFxuICAgICAgICAgICAgICAgICAgICBvbkluc3RydW1lbnRhdGlvblJlcXVlc3RFcnJvcjogKGVycm9yLCBfcmVxdWVzdCwgZXJyb3JDb250ZXh0KT0+cm91dGVNb2R1bGUub25SZXF1ZXN0RXJyb3IocmVxLCBlcnJvciwgZXJyb3JDb250ZXh0LCByb3V0ZXJTZXJ2ZXJDb250ZXh0KSxcbiAgICAgICAgICAgICAgICAgICAgZXJyOiBnZXRSZXF1ZXN0TWV0YShyZXEsICdpbnZva2VFcnJvcicpLFxuICAgICAgICAgICAgICAgICAgICBkZXY6IHJvdXRlTW9kdWxlLmlzRGV2XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGludm9rZVJvdXRlTW9kdWxlKHNwYW4sIGNvbnRleHQpO1xuICAgICAgICAgICAgY29uc3QgeyBtZXRhZGF0YSB9ID0gcmVzdWx0O1xuICAgICAgICAgICAgY29uc3QgeyBjYWNoZUNvbnRyb2wsIGhlYWRlcnMgPSB7fSwgLy8gQWRkIGFueSBmZXRjaCB0YWdzIHRoYXQgd2VyZSBvbiB0aGUgcGFnZSB0byB0aGUgcmVzcG9uc2UgaGVhZGVycy5cbiAgICAgICAgICAgIGZldGNoVGFnczogY2FjaGVUYWdzIH0gPSBtZXRhZGF0YTtcbiAgICAgICAgICAgIGlmIChjYWNoZVRhZ3MpIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzW05FWFRfQ0FDSEVfVEFHU19IRUFERVJdID0gY2FjaGVUYWdzO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gUHVsbCBhbnkgZmV0Y2ggbWV0cmljcyBmcm9tIHRoZSByZW5kZXIgb250byB0aGUgcmVxdWVzdC5cbiAgICAgICAgICAgIDtcbiAgICAgICAgICAgIHJlcS5mZXRjaE1ldHJpY3MgPSBtZXRhZGF0YS5mZXRjaE1ldHJpY3M7XG4gICAgICAgICAgICAvLyB3ZSBkb24ndCB0aHJvdyBzdGF0aWMgdG8gZHluYW1pYyBlcnJvcnMgaW4gZGV2IGFzIGlzU1NHXG4gICAgICAgICAgICAvLyBpcyBhIGJlc3QgZ3Vlc3MgaW4gZGV2IHNpbmNlIHdlIGRvbid0IGhhdmUgdGhlIHByZXJlbmRlciBwYXNzXG4gICAgICAgICAgICAvLyB0byBrbm93IHdoZXRoZXIgdGhlIHBhdGggaXMgYWN0dWFsbHkgc3RhdGljIG9yIG5vdFxuICAgICAgICAgICAgaWYgKGlzU1NHICYmIChjYWNoZUNvbnRyb2wgPT0gbnVsbCA/IHZvaWQgMCA6IGNhY2hlQ29udHJvbC5yZXZhbGlkYXRlKSA9PT0gMCAmJiAhcm91dGVNb2R1bGUuaXNEZXYgJiYgIWlzUm91dGVQUFJFbmFibGVkKSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgc3RhdGljQmFpbG91dEluZm8gPSBtZXRhZGF0YS5zdGF0aWNCYWlsb3V0SW5mbztcbiAgICAgICAgICAgICAgICBjb25zdCBlcnIgPSBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKGBQYWdlIGNoYW5nZWQgZnJvbSBzdGF0aWMgdG8gZHluYW1pYyBhdCBydW50aW1lICR7cmVzb2x2ZWRQYXRobmFtZX0keyhzdGF0aWNCYWlsb3V0SW5mbyA9PSBudWxsID8gdm9pZCAwIDogc3RhdGljQmFpbG91dEluZm8uZGVzY3JpcHRpb24pID8gYCwgcmVhc29uOiAke3N0YXRpY0JhaWxvdXRJbmZvLmRlc2NyaXB0aW9ufWAgOiBgYH1gICsgYFxcbnNlZSBtb3JlIGhlcmUgaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvYXBwLXN0YXRpYy10by1keW5hbWljLWVycm9yYCksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFMTMyXCIsXG4gICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBpZiAoc3RhdGljQmFpbG91dEluZm8gPT0gbnVsbCA/IHZvaWQgMCA6IHN0YXRpY0JhaWxvdXRJbmZvLnN0YWNrKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHN0YWNrID0gc3RhdGljQmFpbG91dEluZm8uc3RhY2s7XG4gICAgICAgICAgICAgICAgICAgIGVyci5zdGFjayA9IGVyci5tZXNzYWdlICsgc3RhY2suc3Vic3RyaW5nKHN0YWNrLmluZGV4T2YoJ1xcbicpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhyb3cgZXJyO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICB2YWx1ZToge1xuICAgICAgICAgICAgICAgICAgICBraW5kOiBDYWNoZWRSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgICAgICAgICAgICAgIGh0bWw6IHJlc3VsdCxcbiAgICAgICAgICAgICAgICAgICAgaGVhZGVycyxcbiAgICAgICAgICAgICAgICAgICAgcnNjRGF0YTogbWV0YWRhdGEuZmxpZ2h0RGF0YSxcbiAgICAgICAgICAgICAgICAgICAgcG9zdHBvbmVkOiBtZXRhZGF0YS5wb3N0cG9uZWQsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1czogbWV0YWRhdGEuc3RhdHVzQ29kZSxcbiAgICAgICAgICAgICAgICAgICAgc2VnbWVudERhdGE6IG1ldGFkYXRhLnNlZ21lbnREYXRhXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2xcbiAgICAgICAgICAgIH07XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlR2VuZXJhdG9yID0gYXN5bmMgKHsgaGFzUmVzb2x2ZWQsIHByZXZpb3VzQ2FjaGVFbnRyeSwgaXNSZXZhbGlkYXRpbmcsIHNwYW4gfSk9PntcbiAgICAgICAgICAgIGNvbnN0IGlzUHJvZHVjdGlvbiA9IHJvdXRlTW9kdWxlLmlzRGV2ID09PSBmYWxzZTtcbiAgICAgICAgICAgIGNvbnN0IGRpZFJlc3BvbmQgPSBoYXNSZXNvbHZlZCB8fCByZXMud3JpdGFibGVFbmRlZDtcbiAgICAgICAgICAgIC8vIHNraXAgb24tZGVtYW5kIHJldmFsaWRhdGUgaWYgY2FjaGUgaXMgbm90IHByZXNlbnQgYW5kXG4gICAgICAgICAgICAvLyByZXZhbGlkYXRlLWlmLWdlbmVyYXRlZCBpcyBzZXRcbiAgICAgICAgICAgIGlmIChpc09uRGVtYW5kUmV2YWxpZGF0ZSAmJiByZXZhbGlkYXRlT25seUdlbmVyYXRlZCAmJiAhcHJldmlvdXNDYWNoZUVudHJ5ICYmICFtaW5pbWFsTW9kZSkge1xuICAgICAgICAgICAgICAgIGlmIChyb3V0ZXJTZXJ2ZXJDb250ZXh0ID09IG51bGwgPyB2b2lkIDAgOiByb3V0ZXJTZXJ2ZXJDb250ZXh0LnJlbmRlcjQwNCkge1xuICAgICAgICAgICAgICAgICAgICBhd2FpdCByb3V0ZXJTZXJ2ZXJDb250ZXh0LnJlbmRlcjQwNChyZXEsIHJlcyk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzLnN0YXR1c0NvZGUgPSA0MDQ7XG4gICAgICAgICAgICAgICAgICAgIHJlcy5lbmQoJ1RoaXMgcGFnZSBjb3VsZCBub3QgYmUgZm91bmQnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBsZXQgZmFsbGJhY2tNb2RlO1xuICAgICAgICAgICAgaWYgKHByZXJlbmRlckluZm8pIHtcbiAgICAgICAgICAgICAgICBmYWxsYmFja01vZGUgPSBwYXJzZUZhbGxiYWNrRmllbGQocHJlcmVuZGVySW5mby5mYWxsYmFjayk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBXaGVuIHNlcnZpbmcgYSBib3QgcmVxdWVzdCwgd2Ugd2FudCB0byBzZXJ2ZSBhIGJsb2NraW5nIHJlbmRlciBhbmQgbm90XG4gICAgICAgICAgICAvLyB0aGUgcHJlcmVuZGVyZWQgcGFnZS4gVGhpcyBlbnN1cmVzIHRoYXQgdGhlIGNvcnJlY3QgY29udGVudCBpcyBzZXJ2ZWRcbiAgICAgICAgICAgIC8vIHRvIHRoZSBib3QgaW4gdGhlIGhlYWQuXG4gICAgICAgICAgICBpZiAoZmFsbGJhY2tNb2RlID09PSBGYWxsYmFja01vZGUuUFJFUkVOREVSICYmIGlzQm90KHVzZXJBZ2VudCkpIHtcbiAgICAgICAgICAgICAgICBmYWxsYmFja01vZGUgPSBGYWxsYmFja01vZGUuQkxPQ0tJTkdfU1RBVElDX1JFTkRFUjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICgocHJldmlvdXNDYWNoZUVudHJ5ID09IG51bGwgPyB2b2lkIDAgOiBwcmV2aW91c0NhY2hlRW50cnkuaXNTdGFsZSkgPT09IC0xKSB7XG4gICAgICAgICAgICAgICAgaXNPbkRlbWFuZFJldmFsaWRhdGUgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gVE9ETzogYWRhcHQgZm9yIFBQUlxuICAgICAgICAgICAgLy8gb25seSBhbGxvdyBvbi1kZW1hbmQgcmV2YWxpZGF0ZSBmb3IgZmFsbGJhY2s6IHRydWUvYmxvY2tpbmdcbiAgICAgICAgICAgIC8vIG9yIGZvciBwcmVyZW5kZXJlZCBmYWxsYmFjazogZmFsc2UgcGF0aHNcbiAgICAgICAgICAgIGlmIChpc09uRGVtYW5kUmV2YWxpZGF0ZSAmJiAoZmFsbGJhY2tNb2RlICE9PSBGYWxsYmFja01vZGUuTk9UX0ZPVU5EIHx8IHByZXZpb3VzQ2FjaGVFbnRyeSkpIHtcbiAgICAgICAgICAgICAgICBmYWxsYmFja01vZGUgPSBGYWxsYmFja01vZGUuQkxPQ0tJTkdfU1RBVElDX1JFTkRFUjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghbWluaW1hbE1vZGUgJiYgZmFsbGJhY2tNb2RlICE9PSBGYWxsYmFja01vZGUuQkxPQ0tJTkdfU1RBVElDX1JFTkRFUiAmJiBzdGF0aWNQYXRoS2V5ICYmICFkaWRSZXNwb25kICYmICFpc0RyYWZ0TW9kZSAmJiBwYWdlSXNEeW5hbWljICYmIChpc1Byb2R1Y3Rpb24gfHwgIWlzUHJlcmVuZGVyZWQpKSB7XG4gICAgICAgICAgICAgICAgLy8gaWYgdGhlIHBhZ2UgaGFzIGR5bmFtaWNQYXJhbXM6IGZhbHNlIGFuZCB0aGlzIHBhdGhuYW1lIHdhc24ndFxuICAgICAgICAgICAgICAgIC8vIHByZXJlbmRlcmVkIHRyaWdnZXIgdGhlIG5vIGZhbGxiYWNrIGhhbmRsaW5nXG4gICAgICAgICAgICAgICAgaWYgKC8vIEluIGRldmVsb3BtZW50LCBmYWxsIHRocm91Z2ggdG8gcmVuZGVyIHRvIGhhbmRsZSBtaXNzaW5nXG4gICAgICAgICAgICAgICAgLy8gZ2V0U3RhdGljUGF0aHMuXG4gICAgICAgICAgICAgICAgKGlzUHJvZHVjdGlvbiB8fCBwcmVyZW5kZXJJbmZvKSAmJiAvLyBXaGVuIGZhbGxiYWNrIGlzbid0IHByZXNlbnQsIGFib3J0IHRoaXMgcmVuZGVyIHNvIHdlIDQwNFxuICAgICAgICAgICAgICAgIGZhbGxiYWNrTW9kZSA9PT0gRmFsbGJhY2tNb2RlLk5PVF9GT1VORCkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgTm9GYWxsYmFja0Vycm9yKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGxldCBmYWxsYmFja1Jlc3BvbnNlO1xuICAgICAgICAgICAgICAgIGlmIChpc1JvdXRlUFBSRW5hYmxlZCAmJiAhaXNSU0NSZXF1ZXN0KSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFdlIHVzZSB0aGUgcmVzcG9uc2UgY2FjaGUgaGVyZSB0byBoYW5kbGUgdGhlIHJldmFsaWRhdGlvbiBhbmRcbiAgICAgICAgICAgICAgICAgICAgLy8gbWFuYWdlbWVudCBvZiB0aGUgZmFsbGJhY2sgc2hlbGwuXG4gICAgICAgICAgICAgICAgICAgIGZhbGxiYWNrUmVzcG9uc2UgPSBhd2FpdCByb3V0ZU1vZHVsZS5oYW5kbGVSZXNwb25zZSh7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUtleTogaXNQcm9kdWN0aW9uID8gbm9ybWFsaXplZFNyY1BhZ2UgOiBudWxsLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgICAgICAgICAgbmV4dENvbmZpZyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJvdXRlS2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICAgICAgICAgICAgICAgICAgaXNGYWxsYmFjazogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHByZXJlbmRlck1hbmlmZXN0LFxuICAgICAgICAgICAgICAgICAgICAgICAgaXNSb3V0ZVBQUkVuYWJsZWQsXG4gICAgICAgICAgICAgICAgICAgICAgICByZXNwb25zZUdlbmVyYXRvcjogYXN5bmMgKCk9PmRvUmVuZGVyKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3BhbixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gV2UgcGFzcyBgdW5kZWZpbmVkYCBhcyByZW5kZXJpbmcgYSBmYWxsYmFjayBpc24ndCByZXN1bWVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGhlcmUuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBvc3Rwb25lZDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmYWxsYmFja1JvdXRlUGFyYW1zOiAvLyBJZiB3ZSdyZSBpbiBwcm9kdWN0aW9uIG9yIHdlJ3JlIGRlYnVnZ2luZyB0aGUgZmFsbGJhY2tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gc2hlbGwgdGhlbiB3ZSBzaG91bGQgcG9zdHBvbmUgd2hlbiBkeW5hbWljIHBhcmFtcyBhcmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gYWNjZXNzZWQuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzUHJvZHVjdGlvbiB8fCBpc0RlYnVnRmFsbGJhY2tTaGVsbCA/IGdldEZhbGxiYWNrUm91dGVQYXJhbXMobm9ybWFsaXplZFNyY1BhZ2UpIDogbnVsbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgICAgICAgICAgd2FpdFVudGlsOiBjdHgud2FpdFVudGlsXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAvLyBJZiB0aGUgZmFsbGJhY2sgcmVzcG9uc2Ugd2FzIHNldCB0byBudWxsLCB0aGVuIHdlIHNob3VsZCByZXR1cm4gbnVsbC5cbiAgICAgICAgICAgICAgICAgICAgaWYgKGZhbGxiYWNrUmVzcG9uc2UgPT09IG51bGwpIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgICAgICAvLyBPdGhlcndpc2UsIGlmIHdlIGRpZCBnZXQgYSBmYWxsYmFjayByZXNwb25zZSwgd2Ugc2hvdWxkIHJldHVybiBpdC5cbiAgICAgICAgICAgICAgICAgICAgaWYgKGZhbGxiYWNrUmVzcG9uc2UpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFJlbW92ZSB0aGUgY2FjaGUgY29udHJvbCBmcm9tIHRoZSByZXNwb25zZSB0byBwcmV2ZW50IGl0IGZyb20gYmVpbmdcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIHVzZWQgaW4gdGhlIHN1cnJvdW5kaW5nIGNhY2hlLlxuICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRlIGZhbGxiYWNrUmVzcG9uc2UuY2FjaGVDb250cm9sO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbGxiYWNrUmVzcG9uc2U7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBPbmx5IHJlcXVlc3RzIHRoYXQgYXJlbid0IHJldmFsaWRhdGluZyBjYW4gYmUgcmVzdW1lZC4gSWYgd2UgaGF2ZSB0aGVcbiAgICAgICAgICAgIC8vIG1pbmltYWwgcG9zdHBvbmVkIGRhdGEsIHRoZW4gd2Ugc2hvdWxkIHJlc3VtZSB0aGUgcmVuZGVyIHdpdGggaXQuXG4gICAgICAgICAgICBjb25zdCBwb3N0cG9uZWQgPSAhaXNPbkRlbWFuZFJldmFsaWRhdGUgJiYgIWlzUmV2YWxpZGF0aW5nICYmIG1pbmltYWxQb3N0cG9uZWQgPyBtaW5pbWFsUG9zdHBvbmVkIDogdW5kZWZpbmVkO1xuICAgICAgICAgICAgLy8gV2hlbiB3ZSdyZSBpbiBtaW5pbWFsIG1vZGUsIGlmIHdlJ3JlIHRyeWluZyB0byBkZWJ1ZyB0aGUgc3RhdGljIHNoZWxsLFxuICAgICAgICAgICAgLy8gd2Ugc2hvdWxkIGp1c3QgcmV0dXJuIG5vdGhpbmcgaW5zdGVhZCBvZiByZXN1bWluZyB0aGUgZHluYW1pYyByZW5kZXIuXG4gICAgICAgICAgICBpZiAoKGlzRGVidWdTdGF0aWNTaGVsbCB8fCBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzKSAmJiB0eXBlb2YgcG9zdHBvbmVkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDoge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGV4cGlyZTogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBraW5kOiBDYWNoZWRSb3V0ZUtpbmQuUEFHRVMsXG4gICAgICAgICAgICAgICAgICAgICAgICBodG1sOiBSZW5kZXJSZXN1bHQuZnJvbVN0YXRpYygnJyksXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWdlRGF0YToge30sXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWFkZXJzOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIHRoaXMgaXMgYSBkeW5hbWljIHJvdXRlIHdpdGggUFBSIGVuYWJsZWQgYW5kIHRoZSBkZWZhdWx0IHJvdXRlXG4gICAgICAgICAgICAvLyBtYXRjaGVzIHdlcmUgc2V0LCB0aGVuIHdlIHNob3VsZCBwYXNzIHRoZSBmYWxsYmFjayByb3V0ZSBwYXJhbXMgdG9cbiAgICAgICAgICAgIC8vIHRoZSByZW5kZXJlciBhcyB0aGlzIGlzIGEgZmFsbGJhY2sgcmV2YWxpZGF0aW9uIHJlcXVlc3QuXG4gICAgICAgICAgICBjb25zdCBmYWxsYmFja1JvdXRlUGFyYW1zID0gcGFnZUlzRHluYW1pYyAmJiBpc1JvdXRlUFBSRW5hYmxlZCAmJiAoZ2V0UmVxdWVzdE1ldGEocmVxLCAncmVuZGVyRmFsbGJhY2tTaGVsbCcpIHx8IGlzRGVidWdGYWxsYmFja1NoZWxsKSA/IGdldEZhbGxiYWNrUm91dGVQYXJhbXMocGF0aG5hbWUpIDogbnVsbDtcbiAgICAgICAgICAgIC8vIFBlcmZvcm0gdGhlIHJlbmRlci5cbiAgICAgICAgICAgIHJldHVybiBkb1JlbmRlcih7XG4gICAgICAgICAgICAgICAgc3BhbixcbiAgICAgICAgICAgICAgICBwb3N0cG9uZWQsXG4gICAgICAgICAgICAgICAgZmFsbGJhY2tSb3V0ZVBhcmFtc1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IGhhbmRsZVJlc3BvbnNlID0gYXN5bmMgKHNwYW4pPT57XG4gICAgICAgICAgICB2YXIgX2NhY2hlRW50cnlfdmFsdWUsIF9jYWNoZWREYXRhX2hlYWRlcnM7XG4gICAgICAgICAgICBjb25zdCBjYWNoZUVudHJ5ID0gYXdhaXQgcm91dGVNb2R1bGUuaGFuZGxlUmVzcG9uc2Uoe1xuICAgICAgICAgICAgICAgIGNhY2hlS2V5OiBzc2dDYWNoZUtleSxcbiAgICAgICAgICAgICAgICByZXNwb25zZUdlbmVyYXRvcjogKGMpPT5yZXNwb25zZUdlbmVyYXRvcih7XG4gICAgICAgICAgICAgICAgICAgICAgICBzcGFuLFxuICAgICAgICAgICAgICAgICAgICAgICAgLi4uY1xuICAgICAgICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgICByb3V0ZUtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgICAgICAgICBpc09uRGVtYW5kUmV2YWxpZGF0ZSxcbiAgICAgICAgICAgICAgICBpc1JvdXRlUFBSRW5hYmxlZCxcbiAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgbmV4dENvbmZpZyxcbiAgICAgICAgICAgICAgICBwcmVyZW5kZXJNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICB3YWl0VW50aWw6IGN0eC53YWl0VW50aWxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKGlzRHJhZnRNb2RlKSB7XG4gICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcignQ2FjaGUtQ29udHJvbCcsICdwcml2YXRlLCBuby1jYWNoZSwgbm8tc3RvcmUsIG1heC1hZ2U9MCwgbXVzdC1yZXZhbGlkYXRlJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBJbiBkZXYsIHdlIHNob3VsZCBub3QgY2FjaGUgcGFnZXMgZm9yIGFueSByZWFzb24uXG4gICAgICAgICAgICBpZiAocm91dGVNb2R1bGUuaXNEZXYpIHtcbiAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKCdDYWNoZS1Db250cm9sJywgJ25vLXN0b3JlLCBtdXN0LXJldmFsaWRhdGUnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghY2FjaGVFbnRyeSkge1xuICAgICAgICAgICAgICAgIGlmIChzc2dDYWNoZUtleSkge1xuICAgICAgICAgICAgICAgICAgICAvLyBBIGNhY2hlIGVudHJ5IG1pZ2h0IG5vdCBiZSBnZW5lcmF0ZWQgaWYgYSByZXNwb25zZSBpcyB3cml0dGVuXG4gICAgICAgICAgICAgICAgICAgIC8vIGluIGBnZXRJbml0aWFsUHJvcHNgIG9yIGBnZXRTZXJ2ZXJTaWRlUHJvcHNgLCBidXQgdGhvc2Ugc2hvdWxkbid0XG4gICAgICAgICAgICAgICAgICAgIC8vIGhhdmUgYSBjYWNoZSBrZXkuIElmIHdlIGRvIGhhdmUgYSBjYWNoZSBrZXkgYnV0IHdlIGRvbid0IGVuZCB1cFxuICAgICAgICAgICAgICAgICAgICAvLyB3aXRoIGEgY2FjaGUgZW50cnksIHRoZW4gZWl0aGVyIE5leHQuanMgb3IgdGhlIGFwcGxpY2F0aW9uIGhhcyBhXG4gICAgICAgICAgICAgICAgICAgIC8vIGJ1ZyB0aGF0IG5lZWRzIGZpeGluZy5cbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcignaW52YXJpYW50OiBjYWNoZSBlbnRyeSByZXF1aXJlZCBidXQgbm90IGdlbmVyYXRlZCcpLCBcIl9fTkVYVF9FUlJPUl9DT0RFXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkU2MlwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCgoX2NhY2hlRW50cnlfdmFsdWUgPSBjYWNoZUVudHJ5LnZhbHVlKSA9PSBudWxsID8gdm9pZCAwIDogX2NhY2hlRW50cnlfdmFsdWUua2luZCkgIT09IENhY2hlZFJvdXRlS2luZC5BUFBfUEFHRSkge1xuICAgICAgICAgICAgICAgIHZhciBfY2FjaGVFbnRyeV92YWx1ZTE7XG4gICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcihgSW52YXJpYW50IGFwcC1wYWdlIGhhbmRsZXIgcmVjZWl2ZWQgaW52YWxpZCBjYWNoZSBlbnRyeSAkeyhfY2FjaGVFbnRyeV92YWx1ZTEgPSBjYWNoZUVudHJ5LnZhbHVlKSA9PSBudWxsID8gdm9pZCAwIDogX2NhY2hlRW50cnlfdmFsdWUxLmtpbmR9YCksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFNzA3XCIsXG4gICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGRpZFBvc3Rwb25lID0gdHlwZW9mIGNhY2hlRW50cnkudmFsdWUucG9zdHBvbmVkID09PSAnc3RyaW5nJztcbiAgICAgICAgICAgIGlmIChpc1NTRyAmJiAvLyBXZSBkb24ndCB3YW50IHRvIHNlbmQgYSBjYWNoZSBoZWFkZXIgZm9yIHJlcXVlc3RzIHRoYXQgY29udGFpbiBkeW5hbWljXG4gICAgICAgICAgICAvLyBkYXRhLiBJZiB0aGlzIGlzIGEgRHluYW1pYyBSU0MgcmVxdWVzdCBvciB3YXNuJ3QgYSBQcmVmZXRjaCBSU0NcbiAgICAgICAgICAgIC8vIHJlcXVlc3QsIHRoZW4gd2Ugc2hvdWxkIHNldCB0aGUgY2FjaGUgaGVhZGVyLlxuICAgICAgICAgICAgIWlzRHluYW1pY1JTQ1JlcXVlc3QgJiYgKCFkaWRQb3N0cG9uZSB8fCBpc1ByZWZldGNoUlNDUmVxdWVzdCkpIHtcbiAgICAgICAgICAgICAgICBpZiAoIW1pbmltYWxNb2RlKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIHNldCB4LW5leHRqcy1jYWNoZSBoZWFkZXIgdG8gbWF0Y2ggdGhlIGhlYWRlclxuICAgICAgICAgICAgICAgICAgICAvLyB3ZSBzZXQgZm9yIHRoZSBpbWFnZS1vcHRpbWl6ZXJcbiAgICAgICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcigneC1uZXh0anMtY2FjaGUnLCBpc09uRGVtYW5kUmV2YWxpZGF0ZSA/ICdSRVZBTElEQVRFRCcgOiBjYWNoZUVudHJ5LmlzTWlzcyA/ICdNSVNTJyA6IGNhY2hlRW50cnkuaXNTdGFsZSA/ICdTVEFMRScgOiAnSElUJyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIFNldCBhIGhlYWRlciB1c2VkIGJ5IHRoZSBjbGllbnQgcm91dGVyIHRvIHNpZ25hbCB0aGUgcmVzcG9uc2UgaXMgc3RhdGljXG4gICAgICAgICAgICAgICAgLy8gYW5kIHNob3VsZCByZXNwZWN0IHRoZSBgc3RhdGljYCBjYWNoZSBzdGFsZVRpbWUgdmFsdWUuXG4gICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcihORVhUX0lTX1BSRVJFTkRFUl9IRUFERVIsICcxJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCB7IHZhbHVlOiBjYWNoZWREYXRhIH0gPSBjYWNoZUVudHJ5O1xuICAgICAgICAgICAgLy8gQ29lcmNlIHRoZSBjYWNoZSBjb250cm9sIHBhcmFtZXRlciBmcm9tIHRoZSByZW5kZXIuXG4gICAgICAgICAgICBsZXQgY2FjaGVDb250cm9sO1xuICAgICAgICAgICAgLy8gSWYgdGhpcyBpcyBhIHJlc3VtZSByZXF1ZXN0IGluIG1pbmltYWwgbW9kZSBpdCBpcyBzdHJlYW1lZCB3aXRoIGR5bmFtaWNcbiAgICAgICAgICAgIC8vIGNvbnRlbnQgYW5kIHNob3VsZCBub3QgYmUgY2FjaGVkLlxuICAgICAgICAgICAgaWYgKG1pbmltYWxQb3N0cG9uZWQpIHtcbiAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2wgPSB7XG4gICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGU6IDAsXG4gICAgICAgICAgICAgICAgICAgIGV4cGlyZTogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAobWluaW1hbE1vZGUgJiYgaXNSU0NSZXF1ZXN0ICYmICFpc1ByZWZldGNoUlNDUmVxdWVzdCAmJiBpc1JvdXRlUFBSRW5hYmxlZCkge1xuICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbCA9IHtcbiAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMCxcbiAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSBlbHNlIGlmICghcm91dGVNb2R1bGUuaXNEZXYpIHtcbiAgICAgICAgICAgICAgICAvLyBJZiB0aGlzIGlzIGEgcHJldmlldyBtb2RlIHJlcXVlc3QsIHdlIHNob3VsZG4ndCBjYWNoZSBpdFxuICAgICAgICAgICAgICAgIGlmIChpc0RyYWZ0TW9kZSkge1xuICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2wgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlOiAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKCFpc1NTRykge1xuICAgICAgICAgICAgICAgICAgICBpZiAoIXJlcy5nZXRIZWFkZXIoJ0NhY2hlLUNvbnRyb2wnKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGU6IDAsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGNhY2hlRW50cnkuY2FjaGVDb250cm9sKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIElmIHRoZSBjYWNoZSBlbnRyeSBoYXMgYSBjYWNoZSBjb250cm9sIHdpdGggYSByZXZhbGlkYXRlIHZhbHVlIHRoYXQnc1xuICAgICAgICAgICAgICAgICAgICAvLyBhIG51bWJlciwgdXNlIGl0LlxuICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGNhY2hlRW50cnkuY2FjaGVDb250cm9sLnJldmFsaWRhdGUgPT09ICdudW1iZXInKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YXIgX2NhY2hlRW50cnlfY2FjaGVDb250cm9sO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGNhY2hlRW50cnkuY2FjaGVDb250cm9sLnJldmFsaWRhdGUgPCAxKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcihgSW52YWxpZCByZXZhbGlkYXRlIGNvbmZpZ3VyYXRpb24gcHJvdmlkZWQ6ICR7Y2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wucmV2YWxpZGF0ZX0gPCAxYCksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFMjJcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGU6IGNhY2hlRW50cnkuY2FjaGVDb250cm9sLnJldmFsaWRhdGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiAoKF9jYWNoZUVudHJ5X2NhY2hlQ29udHJvbCA9IGNhY2hlRW50cnkuY2FjaGVDb250cm9sKSA9PSBudWxsID8gdm9pZCAwIDogX2NhY2hlRW50cnlfY2FjaGVDb250cm9sLmV4cGlyZSkgPz8gbmV4dENvbmZpZy5leHBpcmVUaW1lXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGU6IENBQ0hFX09ORV9ZRUFSLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4cGlyZTogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wgPSBjYWNoZUNvbnRyb2w7XG4gICAgICAgICAgICBpZiAodHlwZW9mIHNlZ21lbnRQcmVmZXRjaEhlYWRlciA9PT0gJ3N0cmluZycgJiYgKGNhY2hlZERhdGEgPT0gbnVsbCA/IHZvaWQgMCA6IGNhY2hlZERhdGEua2luZCkgPT09IENhY2hlZFJvdXRlS2luZC5BUFBfUEFHRSAmJiBjYWNoZWREYXRhLnNlZ21lbnREYXRhKSB7XG4gICAgICAgICAgICAgICAgdmFyIF9jYWNoZWREYXRhX2hlYWRlcnMxO1xuICAgICAgICAgICAgICAgIC8vIFRoaXMgaXMgYSBwcmVmZXRjaCByZXF1ZXN0IGlzc3VlZCBieSB0aGUgY2xpZW50IFNlZ21lbnQgQ2FjaGUuIFRoZXNlXG4gICAgICAgICAgICAgICAgLy8gc2hvdWxkIG5ldmVyIHJlYWNoIHRoZSBhcHBsaWNhdGlvbiBsYXllciAobGFtYmRhKS4gV2Ugc2hvdWxkIGVpdGhlclxuICAgICAgICAgICAgICAgIC8vIHJlc3BvbmQgZnJvbSB0aGUgY2FjaGUgKEhJVCkgb3IgcmVzcG9uZCB3aXRoIDIwNCBObyBDb250ZW50IChNSVNTKS5cbiAgICAgICAgICAgICAgICAvLyBTZXQgYSBoZWFkZXIgdG8gaW5kaWNhdGUgdGhhdCBQUFIgaXMgZW5hYmxlZCBmb3IgdGhpcyByb3V0ZS4gVGhpc1xuICAgICAgICAgICAgICAgIC8vIGxldHMgdGhlIGNsaWVudCBkaXN0aW5ndWlzaCBiZXR3ZWVuIGEgcmVndWxhciBjYWNoZSBtaXNzIGFuZCBhIGNhY2hlXG4gICAgICAgICAgICAgICAgLy8gbWlzcyBkdWUgdG8gUFBSIGJlaW5nIGRpc2FibGVkLiBJbiBvdGhlciBjb250ZXh0cyB0aGlzIGhlYWRlciBpcyB1c2VkXG4gICAgICAgICAgICAgICAgLy8gdG8gaW5kaWNhdGUgdGhhdCB0aGUgcmVzcG9uc2UgY29udGFpbnMgZHluYW1pYyBkYXRhLCBidXQgaGVyZSB3ZSdyZVxuICAgICAgICAgICAgICAgIC8vIG9ubHkgdXNpbmcgaXQgdG8gaW5kaWNhdGUgdGhhdCB0aGUgZmVhdHVyZSBpcyBlbmFibGVkIOKAlCB0aGUgc2VnbWVudFxuICAgICAgICAgICAgICAgIC8vIHJlc3BvbnNlIGl0c2VsZiBjb250YWlucyB3aGV0aGVyIHRoZSBkYXRhIGlzIGR5bmFtaWMuXG4gICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcihORVhUX0RJRF9QT1NUUE9ORV9IRUFERVIsICcyJyk7XG4gICAgICAgICAgICAgICAgLy8gQWRkIHRoZSBjYWNoZSB0YWdzIGhlYWRlciB0byB0aGUgcmVzcG9uc2UgaWYgaXQgZXhpc3RzIGFuZCB3ZSdyZSBpblxuICAgICAgICAgICAgICAgIC8vIG1pbmltYWwgbW9kZSB3aGlsZSByZW5kZXJpbmcgYSBzdGF0aWMgcGFnZS5cbiAgICAgICAgICAgICAgICBjb25zdCB0YWdzID0gKF9jYWNoZWREYXRhX2hlYWRlcnMxID0gY2FjaGVkRGF0YS5oZWFkZXJzKSA9PSBudWxsID8gdm9pZCAwIDogX2NhY2hlZERhdGFfaGVhZGVyczFbTkVYVF9DQUNIRV9UQUdTX0hFQURFUl07XG4gICAgICAgICAgICAgICAgaWYgKG1pbmltYWxNb2RlICYmIGlzU1NHICYmIHRhZ3MgJiYgdHlwZW9mIHRhZ3MgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlcy5zZXRIZWFkZXIoTkVYVF9DQUNIRV9UQUdTX0hFQURFUiwgdGFncyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IG1hdGNoZWRTZWdtZW50ID0gY2FjaGVkRGF0YS5zZWdtZW50RGF0YS5nZXQoc2VnbWVudFByZWZldGNoSGVhZGVyKTtcbiAgICAgICAgICAgICAgICBpZiAobWF0Y2hlZFNlZ21lbnQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICAvLyBDYWNoZSBoaXRcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHNlbmRSZW5kZXJSZXN1bHQoe1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzLFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3JzYycsXG4gICAgICAgICAgICAgICAgICAgICAgICBnZW5lcmF0ZUV0YWdzOiBuZXh0Q29uZmlnLmdlbmVyYXRlRXRhZ3MsXG4gICAgICAgICAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiBSZW5kZXJSZXN1bHQuZnJvbVN0YXRpYyhtYXRjaGVkU2VnbWVudCksXG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2w6IGNhY2hlRW50cnkuY2FjaGVDb250cm9sXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyBDYWNoZSBtaXNzLiBFaXRoZXIgYSBjYWNoZSBlbnRyeSBmb3IgdGhpcyByb3V0ZSBoYXMgbm90IGJlZW4gZ2VuZXJhdGVkXG4gICAgICAgICAgICAgICAgLy8gKHdoaWNoIHRlY2huaWNhbGx5IHNob3VsZCBub3QgYmUgcG9zc2libGUgd2hlbiBQUFIgaXMgZW5hYmxlZCwgYmVjYXVzZVxuICAgICAgICAgICAgICAgIC8vIGF0IGEgbWluaW11bSB0aGVyZSBzaG91bGQgYWx3YXlzIGJlIGEgZmFsbGJhY2sgZW50cnkpIG9yIHRoZXJlJ3Mgbm9cbiAgICAgICAgICAgICAgICAvLyBtYXRjaCBmb3IgdGhlIHJlcXVlc3RlZCBzZWdtZW50LiBSZXNwb25kIHdpdGggYSAyMDQgTm8gQ29udGVudC4gV2VcbiAgICAgICAgICAgICAgICAvLyBkb24ndCBib3RoZXIgdG8gcmVzcG9uZCB3aXRoIDQwNCwgYmVjYXVzZSB0aGVzZSByZXF1ZXN0cyBhcmUgb25seVxuICAgICAgICAgICAgICAgIC8vIGlzc3VlZCBhcyBwYXJ0IG9mIGEgcHJlZmV0Y2guXG4gICAgICAgICAgICAgICAgcmVzLnN0YXR1c0NvZGUgPSAyMDQ7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHNlbmRSZW5kZXJSZXN1bHQoe1xuICAgICAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgICAgIHJlcyxcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3JzYycsXG4gICAgICAgICAgICAgICAgICAgIGdlbmVyYXRlRXRhZ3M6IG5leHRDb25maWcuZ2VuZXJhdGVFdGFncyxcbiAgICAgICAgICAgICAgICAgICAgcG93ZXJlZEJ5SGVhZGVyOiBuZXh0Q29uZmlnLnBvd2VyZWRCeUhlYWRlcixcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiBSZW5kZXJSZXN1bHQuZnJvbVN0YXRpYygnJyksXG4gICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDogY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2xcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIHRoZXJlJ3MgYSBjYWxsYmFjayBmb3IgYG9uQ2FjaGVFbnRyeWAsIGNhbGwgaXQgd2l0aCB0aGUgY2FjaGUgZW50cnlcbiAgICAgICAgICAgIC8vIGFuZCB0aGUgcmV2YWxpZGF0ZSBvcHRpb25zLlxuICAgICAgICAgICAgY29uc3Qgb25DYWNoZUVudHJ5ID0gZ2V0UmVxdWVzdE1ldGEocmVxLCAnb25DYWNoZUVudHJ5Jyk7XG4gICAgICAgICAgICBpZiAob25DYWNoZUVudHJ5KSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmluaXNoZWQgPSBhd2FpdCBvbkNhY2hlRW50cnkoe1xuICAgICAgICAgICAgICAgICAgICAuLi5jYWNoZUVudHJ5LFxuICAgICAgICAgICAgICAgICAgICAvLyBUT0RPOiByZW1vdmUgdGhpcyB3aGVuIHVwc3RyZWFtIGRvZXNuJ3RcbiAgICAgICAgICAgICAgICAgICAgLy8gYWx3YXlzIGV4cGVjdCB0aGlzIHZhbHVlIHRvIGJlIFwiUEFHRVwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5jYWNoZUVudHJ5LnZhbHVlLFxuICAgICAgICAgICAgICAgICAgICAgICAga2luZDogJ1BBR0UnXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9LCB7XG4gICAgICAgICAgICAgICAgICAgIHVybDogZ2V0UmVxdWVzdE1ldGEocmVxLCAnaW5pdFVSTCcpXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgaWYgKGZpbmlzaGVkKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFRPRE86IG1heWJlIHdlIGhhdmUgdG8gZW5kIHRoZSByZXF1ZXN0P1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBJZiB0aGUgcmVxdWVzdCBoYXMgYSBwb3N0cG9uZWQgc3RhdGUgYW5kIGl0J3MgYSByZXN1bWUgcmVxdWVzdCB3ZVxuICAgICAgICAgICAgLy8gc2hvdWxkIGVycm9yLlxuICAgICAgICAgICAgaWYgKGRpZFBvc3Rwb25lICYmIG1pbmltYWxQb3N0cG9uZWQpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKCdJbnZhcmlhbnQ6IHBvc3Rwb25lZCBzdGF0ZSBzaG91bGQgbm90IGJlIHByZXNlbnQgb24gYSByZXN1bWUgcmVxdWVzdCcpLCBcIl9fTkVYVF9FUlJPUl9DT0RFXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFwiRTM5NlwiLFxuICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoY2FjaGVkRGF0YS5oZWFkZXJzKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgaGVhZGVycyA9IHtcbiAgICAgICAgICAgICAgICAgICAgLi4uY2FjaGVkRGF0YS5oZWFkZXJzXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICBpZiAoIW1pbmltYWxNb2RlIHx8ICFpc1NTRykge1xuICAgICAgICAgICAgICAgICAgICBkZWxldGUgaGVhZGVyc1tORVhUX0NBQ0hFX1RBR1NfSEVBREVSXTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZm9yIChsZXQgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKGhlYWRlcnMpKXtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ3VuZGVmaW5lZCcpIGNvbnRpbnVlO1xuICAgICAgICAgICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgdiBvZiB2YWx1ZSl7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzLmFwcGVuZEhlYWRlcihrZXksIHYpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlID0gdmFsdWUudG9TdHJpbmcoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcy5hcHBlbmRIZWFkZXIoa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXMuYXBwZW5kSGVhZGVyKGtleSwgdmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gQWRkIHRoZSBjYWNoZSB0YWdzIGhlYWRlciB0byB0aGUgcmVzcG9uc2UgaWYgaXQgZXhpc3RzIGFuZCB3ZSdyZSBpblxuICAgICAgICAgICAgLy8gbWluaW1hbCBtb2RlIHdoaWxlIHJlbmRlcmluZyBhIHN0YXRpYyBwYWdlLlxuICAgICAgICAgICAgY29uc3QgdGFncyA9IChfY2FjaGVkRGF0YV9oZWFkZXJzID0gY2FjaGVkRGF0YS5oZWFkZXJzKSA9PSBudWxsID8gdm9pZCAwIDogX2NhY2hlZERhdGFfaGVhZGVyc1tORVhUX0NBQ0hFX1RBR1NfSEVBREVSXTtcbiAgICAgICAgICAgIGlmIChtaW5pbWFsTW9kZSAmJiBpc1NTRyAmJiB0YWdzICYmIHR5cGVvZiB0YWdzID09PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgICAgIHJlcy5zZXRIZWFkZXIoTkVYVF9DQUNIRV9UQUdTX0hFQURFUiwgdGFncyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBJZiB0aGUgcmVxdWVzdCBpcyBhIGRhdGEgcmVxdWVzdCwgdGhlbiB3ZSBzaG91bGRuJ3Qgc2V0IHRoZSBzdGF0dXMgY29kZVxuICAgICAgICAgICAgLy8gZnJvbSB0aGUgcmVzcG9uc2UgYmVjYXVzZSBpdCBzaG91bGQgYWx3YXlzIGJlIDIwMC4gVGhpcyBzaG91bGQgYmUgZ2F0ZWRcbiAgICAgICAgICAgIC8vIGJlaGluZCB0aGUgZXhwZXJpbWVudGFsIFBQUiBmbGFnLlxuICAgICAgICAgICAgaWYgKGNhY2hlZERhdGEuc3RhdHVzICYmICghaXNSU0NSZXF1ZXN0IHx8ICFpc1JvdXRlUFBSRW5hYmxlZCkpIHtcbiAgICAgICAgICAgICAgICByZXMuc3RhdHVzQ29kZSA9IGNhY2hlZERhdGEuc3RhdHVzO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gUmVkaXJlY3QgaW5mb3JtYXRpb24gaXMgZW5jb2RlZCBpbiBSU0MgcGF5bG9hZCwgc28gd2UgZG9uJ3QgbmVlZCB0byB1c2UgcmVkaXJlY3Qgc3RhdHVzIGNvZGVzXG4gICAgICAgICAgICBpZiAoIW1pbmltYWxNb2RlICYmIGNhY2hlZERhdGEuc3RhdHVzICYmIFJlZGlyZWN0U3RhdHVzQ29kZVtjYWNoZWREYXRhLnN0YXR1c10gJiYgaXNSU0NSZXF1ZXN0KSB7XG4gICAgICAgICAgICAgICAgcmVzLnN0YXR1c0NvZGUgPSAyMDA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBNYXJrIHRoYXQgdGhlIHJlcXVlc3QgZGlkIHBvc3Rwb25lLlxuICAgICAgICAgICAgaWYgKGRpZFBvc3Rwb25lKSB7XG4gICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcihORVhUX0RJRF9QT1NUUE9ORV9IRUFERVIsICcxJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyB3ZSBkb24ndCBnbyB0aHJvdWdoIHRoaXMgYmxvY2sgd2hlbiBwcmV2aWV3IG1vZGUgaXMgdHJ1ZVxuICAgICAgICAgICAgLy8gYXMgcHJldmlldyBtb2RlIGlzIGEgZHluYW1pYyByZXF1ZXN0IChieXBhc3NlcyBjYWNoZSkgYW5kIGRvZXNuJ3RcbiAgICAgICAgICAgIC8vIGdlbmVyYXRlIGJvdGggSFRNTCBhbmQgcGF5bG9hZHMgaW4gdGhlIHNhbWUgcmVxdWVzdCBzbyBjb250aW51ZSB0byBqdXN0XG4gICAgICAgICAgICAvLyByZXR1cm4gdGhlIGdlbmVyYXRlZCBwYXlsb2FkXG4gICAgICAgICAgICBpZiAoaXNSU0NSZXF1ZXN0ICYmICFpc0RyYWZ0TW9kZSkge1xuICAgICAgICAgICAgICAgIC8vIElmIHRoaXMgaXMgYSBkeW5hbWljIFJTQyByZXF1ZXN0LCB0aGVuIHN0cmVhbSB0aGUgcmVzcG9uc2UuXG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBjYWNoZWREYXRhLnJzY0RhdGEgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChjYWNoZWREYXRhLnBvc3Rwb25lZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcignSW52YXJpYW50OiBFeHBlY3RlZCBwb3N0cG9uZWQgdG8gYmUgdW5kZWZpbmVkJyksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkUzNzJcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBzZW5kUmVuZGVyUmVzdWx0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdyc2MnLFxuICAgICAgICAgICAgICAgICAgICAgICAgZ2VuZXJhdGVFdGFnczogbmV4dENvbmZpZy5nZW5lcmF0ZUV0YWdzLFxuICAgICAgICAgICAgICAgICAgICAgICAgcG93ZXJlZEJ5SGVhZGVyOiBuZXh0Q29uZmlnLnBvd2VyZWRCeUhlYWRlcixcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3VsdDogY2FjaGVkRGF0YS5odG1sLFxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gRHluYW1pYyBSU0MgcmVzcG9uc2VzIGNhbm5vdCBiZSBjYWNoZWQsIGV2ZW4gaWYgdGhleSdyZVxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gY29uZmlndXJlZCB3aXRoIGBmb3JjZS1zdGF0aWNgIGJlY2F1c2Ugd2UgaGF2ZSBubyB3YXkgb2ZcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGRpc3Rpbmd1aXNoaW5nIGJldHdlZW4gYGZvcmNlLXN0YXRpY2AgYW5kIHBhZ2VzIHRoYXQgaGF2ZSBub1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gcG9zdHBvbmVkIHN0YXRlLlxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gVE9ETzogZGlzdGluZ3Vpc2ggYGZvcmNlLXN0YXRpY2AgZnJvbSBwYWdlcyB3aXRoIG5vIHBvc3Rwb25lZCBzdGF0ZSAoc3RhdGljKVxuICAgICAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sOiBpc0R5bmFtaWNSU0NSZXF1ZXN0ID8ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGU6IDAsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gOiBjYWNoZUVudHJ5LmNhY2hlQ29udHJvbFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gQXMgdGhpcyBpc24ndCBhIHByZWZldGNoIHJlcXVlc3QsIHdlIHNob3VsZCBzZXJ2ZSB0aGUgc3RhdGljIGZsaWdodFxuICAgICAgICAgICAgICAgIC8vIGRhdGEuXG4gICAgICAgICAgICAgICAgcmV0dXJuIHNlbmRSZW5kZXJSZXN1bHQoe1xuICAgICAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgICAgIHJlcyxcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3JzYycsXG4gICAgICAgICAgICAgICAgICAgIGdlbmVyYXRlRXRhZ3M6IG5leHRDb25maWcuZ2VuZXJhdGVFdGFncyxcbiAgICAgICAgICAgICAgICAgICAgcG93ZXJlZEJ5SGVhZGVyOiBuZXh0Q29uZmlnLnBvd2VyZWRCeUhlYWRlcixcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiBSZW5kZXJSZXN1bHQuZnJvbVN0YXRpYyhjYWNoZWREYXRhLnJzY0RhdGEpLFxuICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2w6IGNhY2hlRW50cnkuY2FjaGVDb250cm9sXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBUaGlzIGlzIGEgcmVxdWVzdCBmb3IgSFRNTCBkYXRhLlxuICAgICAgICAgICAgbGV0IGJvZHkgPSBjYWNoZWREYXRhLmh0bWw7XG4gICAgICAgICAgICAvLyBJZiB0aGVyZSdzIG5vIHBvc3Rwb25lZCBzdGF0ZSwgd2Ugc2hvdWxkIGp1c3Qgc2VydmUgdGhlIEhUTUwuIFRoaXNcbiAgICAgICAgICAgIC8vIHNob3VsZCBhbHNvIGJlIHRoZSBjYXNlIGZvciBhIHJlc3VtZSByZXF1ZXN0IGJlY2F1c2UgaXQncyBjb21wbGV0ZWRcbiAgICAgICAgICAgIC8vIGFzIGEgc2VydmVyIHJlbmRlciAocmF0aGVyIHRoYW4gYSBzdGF0aWMgcmVuZGVyKS5cbiAgICAgICAgICAgIGlmICghZGlkUG9zdHBvbmUgfHwgbWluaW1hbE1vZGUpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gc2VuZFJlbmRlclJlc3VsdCh7XG4gICAgICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICAgICAgcmVzLFxuICAgICAgICAgICAgICAgICAgICB0eXBlOiAnaHRtbCcsXG4gICAgICAgICAgICAgICAgICAgIGdlbmVyYXRlRXRhZ3M6IG5leHRDb25maWcuZ2VuZXJhdGVFdGFncyxcbiAgICAgICAgICAgICAgICAgICAgcG93ZXJlZEJ5SGVhZGVyOiBuZXh0Q29uZmlnLnBvd2VyZWRCeUhlYWRlcixcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiBib2R5LFxuICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2w6IGNhY2hlRW50cnkuY2FjaGVDb250cm9sXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBJZiB3ZSdyZSBkZWJ1Z2dpbmcgdGhlIHN0YXRpYyBzaGVsbCBvciB0aGUgZHluYW1pYyBBUEkgYWNjZXNzZXMsIHdlXG4gICAgICAgICAgICAvLyBzaG91bGQganVzdCBzZXJ2ZSB0aGUgSFRNTCB3aXRob3V0IHJlc3VtaW5nIHRoZSByZW5kZXIuIFRoZSByZXR1cm5lZFxuICAgICAgICAgICAgLy8gSFRNTCB3aWxsIGJlIHRoZSBzdGF0aWMgc2hlbGwgc28gYWxsIHRoZSBEeW5hbWljIEFQSSdzIHdpbGwgYmUgdXNlZFxuICAgICAgICAgICAgLy8gZHVyaW5nIHN0YXRpYyBnZW5lcmF0aW9uLlxuICAgICAgICAgICAgaWYgKGlzRGVidWdTdGF0aWNTaGVsbCB8fCBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzKSB7XG4gICAgICAgICAgICAgICAgLy8gU2luY2Ugd2UncmUgbm90IHJlc3VtaW5nIHRoZSByZW5kZXIsIHdlIG5lZWQgdG8gYXQgbGVhc3QgYWRkIHRoZVxuICAgICAgICAgICAgICAgIC8vIGNsb3NpbmcgYm9keSBhbmQgaHRtbCB0YWdzIHRvIGNyZWF0ZSB2YWxpZCBIVE1MLlxuICAgICAgICAgICAgICAgIGJvZHkuY2hhaW4obmV3IFJlYWRhYmxlU3RyZWFtKHtcbiAgICAgICAgICAgICAgICAgICAgc3RhcnQgKGNvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZShFTkNPREVEX1RBR1MuQ0xPU0VELkJPRFlfQU5EX0hUTUwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbGxlci5jbG9zZSgpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgIHJldHVybiBzZW5kUmVuZGVyUmVzdWx0KHtcbiAgICAgICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgICAgICByZXMsXG4gICAgICAgICAgICAgICAgICAgIHR5cGU6ICdodG1sJyxcbiAgICAgICAgICAgICAgICAgICAgZ2VuZXJhdGVFdGFnczogbmV4dENvbmZpZy5nZW5lcmF0ZUV0YWdzLFxuICAgICAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICByZXN1bHQ6IGJvZHksXG4gICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDoge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGV4cGlyZTogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIFRoaXMgcmVxdWVzdCBoYXMgcG9zdHBvbmVkLCBzbyBsZXQncyBjcmVhdGUgYSBuZXcgdHJhbnNmb3JtZXIgdGhhdCB0aGVcbiAgICAgICAgICAgIC8vIGR5bmFtaWMgZGF0YSBjYW4gcGlwZSB0byB0aGF0IHdpbGwgYXR0YWNoIHRoZSBkeW5hbWljIGRhdGEgdG8gdGhlIGVuZFxuICAgICAgICAgICAgLy8gb2YgdGhlIHJlc3BvbnNlLlxuICAgICAgICAgICAgY29uc3QgdHJhbnNmb3JtZXIgPSBuZXcgVHJhbnNmb3JtU3RyZWFtKCk7XG4gICAgICAgICAgICBib2R5LmNoYWluKHRyYW5zZm9ybWVyLnJlYWRhYmxlKTtcbiAgICAgICAgICAgIC8vIFBlcmZvcm0gdGhlIHJlbmRlciBhZ2FpbiwgYnV0IHRoaXMgdGltZSwgcHJvdmlkZSB0aGUgcG9zdHBvbmVkIHN0YXRlLlxuICAgICAgICAgICAgLy8gV2UgZG9uJ3QgYXdhaXQgYmVjYXVzZSB3ZSB3YW50IHRoZSByZXN1bHQgdG8gc3RhcnQgc3RyZWFtaW5nIG5vdywgYW5kXG4gICAgICAgICAgICAvLyB3ZSd2ZSBhbHJlYWR5IGNoYWluZWQgdGhlIHRyYW5zZm9ybWVyJ3MgcmVhZGFibGUgdG8gdGhlIHJlbmRlciByZXN1bHQuXG4gICAgICAgICAgICBkb1JlbmRlcih7XG4gICAgICAgICAgICAgICAgc3BhbixcbiAgICAgICAgICAgICAgICBwb3N0cG9uZWQ6IGNhY2hlZERhdGEucG9zdHBvbmVkLFxuICAgICAgICAgICAgICAgIC8vIFRoaXMgaXMgYSByZXN1bWUgcmVuZGVyLCBub3QgYSBmYWxsYmFjayByZW5kZXIsIHNvIHdlIGRvbid0IG5lZWQgdG9cbiAgICAgICAgICAgICAgICAvLyBzZXQgdGhpcy5cbiAgICAgICAgICAgICAgICBmYWxsYmFja1JvdXRlUGFyYW1zOiBudWxsXG4gICAgICAgICAgICB9KS50aGVuKGFzeW5jIChyZXN1bHQpPT57XG4gICAgICAgICAgICAgICAgdmFyIF9yZXN1bHRfdmFsdWU7XG4gICAgICAgICAgICAgICAgaWYgKCFyZXN1bHQpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcignSW52YXJpYW50OiBleHBlY3RlZCBhIHJlc3VsdCB0byBiZSByZXR1cm5lZCcpLCBcIl9fTkVYVF9FUlJPUl9DT0RFXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkU0NjNcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoKChfcmVzdWx0X3ZhbHVlID0gcmVzdWx0LnZhbHVlKSA9PSBudWxsID8gdm9pZCAwIDogX3Jlc3VsdF92YWx1ZS5raW5kKSAhPT0gQ2FjaGVkUm91dGVLaW5kLkFQUF9QQUdFKSB7XG4gICAgICAgICAgICAgICAgICAgIHZhciBfcmVzdWx0X3ZhbHVlMTtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcihgSW52YXJpYW50OiBleHBlY3RlZCBhIHBhZ2UgcmVzcG9uc2UsIGdvdCAkeyhfcmVzdWx0X3ZhbHVlMSA9IHJlc3VsdC52YWx1ZSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9yZXN1bHRfdmFsdWUxLmtpbmR9YCksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFwiRTMwNVwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIFBpcGUgdGhlIHJlc3VtZSByZXN1bHQgdG8gdGhlIHRyYW5zZm9ybWVyLlxuICAgICAgICAgICAgICAgIGF3YWl0IHJlc3VsdC52YWx1ZS5odG1sLnBpcGVUbyh0cmFuc2Zvcm1lci53cml0YWJsZSk7XG4gICAgICAgICAgICB9KS5jYXRjaCgoZXJyKT0+e1xuICAgICAgICAgICAgICAgIC8vIEFuIGVycm9yIG9jY3VycmVkIGR1cmluZyBwaXBpbmcgb3IgcHJlcGFyaW5nIHRoZSByZW5kZXIsIGFib3J0XG4gICAgICAgICAgICAgICAgLy8gdGhlIHRyYW5zZm9ybWVycyB3cml0ZXIgc28gd2UgY2FuIHRlcm1pbmF0ZSB0aGUgc3RyZWFtLlxuICAgICAgICAgICAgICAgIHRyYW5zZm9ybWVyLndyaXRhYmxlLmFib3J0KGVycikuY2F0Y2goKGUpPT57XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJjb3VsZG4ndCBhYm9ydCB0cmFuc2Zvcm1lclwiLCBlKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIHNlbmRSZW5kZXJSZXN1bHQoe1xuICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICByZXMsXG4gICAgICAgICAgICAgICAgdHlwZTogJ2h0bWwnLFxuICAgICAgICAgICAgICAgIGdlbmVyYXRlRXRhZ3M6IG5leHRDb25maWcuZ2VuZXJhdGVFdGFncyxcbiAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgIHJlc3VsdDogYm9keSxcbiAgICAgICAgICAgICAgICAvLyBXZSBkb24ndCB3YW50IHRvIGNhY2hlIHRoZSByZXNwb25zZSBpZiBpdCBoYXMgcG9zdHBvbmVkIGRhdGEgYmVjYXVzZVxuICAgICAgICAgICAgICAgIC8vIHRoZSByZXNwb25zZSBiZWluZyBzZW50IHRvIHRoZSBjbGllbnQgaXQncyBkeW5hbWljIHBhcnRzIGFyZSBzdHJlYW1lZFxuICAgICAgICAgICAgICAgIC8vIHRvIHRoZSBjbGllbnQgb24gdGhlIHNhbWUgcmVxdWVzdC5cbiAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2w6IHtcbiAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMCxcbiAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgLy8gVE9ETzogYWN0aXZlU3BhbiBjb2RlIHBhdGggaXMgZm9yIHdoZW4gd3JhcHBlZCBieVxuICAgICAgICAvLyBuZXh0LXNlcnZlciBjYW4gYmUgcmVtb3ZlZCB3aGVuIHRoaXMgaXMgbm8gbG9uZ2VyIHVzZWRcbiAgICAgICAgaWYgKGFjdGl2ZVNwYW4pIHtcbiAgICAgICAgICAgIGF3YWl0IGhhbmRsZVJlc3BvbnNlKGFjdGl2ZVNwYW4pO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIGF3YWl0IHRyYWNlci53aXRoUHJvcGFnYXRlZENvbnRleHQocmVxLmhlYWRlcnMsICgpPT50cmFjZXIudHJhY2UoQmFzZVNlcnZlclNwYW4uaGFuZGxlUmVxdWVzdCwge1xuICAgICAgICAgICAgICAgICAgICBzcGFuTmFtZTogYCR7bWV0aG9kfSAke3JlcS51cmx9YCxcbiAgICAgICAgICAgICAgICAgICAga2luZDogU3BhbktpbmQuU0VSVkVSLFxuICAgICAgICAgICAgICAgICAgICBhdHRyaWJ1dGVzOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAnaHR0cC5tZXRob2QnOiBtZXRob2QsXG4gICAgICAgICAgICAgICAgICAgICAgICAnaHR0cC50YXJnZXQnOiByZXEudXJsXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9LCBoYW5kbGVSZXNwb25zZSkpO1xuICAgICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIC8vIGlmIHdlIGFyZW4ndCB3cmFwcGVkIGJ5IGJhc2Utc2VydmVyIGhhbmRsZSBoZXJlXG4gICAgICAgIGlmICghYWN0aXZlU3BhbiAmJiAhKGVyciBpbnN0YW5jZW9mIE5vRmFsbGJhY2tFcnJvcikpIHtcbiAgICAgICAgICAgIGF3YWl0IHJvdXRlTW9kdWxlLm9uUmVxdWVzdEVycm9yKHJlcSwgZXJyLCB7XG4gICAgICAgICAgICAgICAgcm91dGVyS2luZDogJ0FwcCBSb3V0ZXInLFxuICAgICAgICAgICAgICAgIHJvdXRlUGF0aDogc3JjUGFnZSxcbiAgICAgICAgICAgICAgICByb3V0ZVR5cGU6ICdyZW5kZXInLFxuICAgICAgICAgICAgICAgIHJldmFsaWRhdGVSZWFzb246IGdldFJldmFsaWRhdGVSZWFzb24oe1xuICAgICAgICAgICAgICAgICAgICBpc1JldmFsaWRhdGU6IGlzU1NHLFxuICAgICAgICAgICAgICAgICAgICBpc09uRGVtYW5kUmV2YWxpZGF0ZVxuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9LCByb3V0ZXJTZXJ2ZXJDb250ZXh0KTtcbiAgICAgICAgfVxuICAgICAgICAvLyByZXRocm93IHNvIHRoYXQgd2UgY2FuIGhhbmRsZSBzZXJ2aW5nIGVycm9yIHBhZ2VcbiAgICAgICAgdGhyb3cgZXJyO1xuICAgIH1cbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=C%3A%5CUsers%5CMicka%5CCascadeProjects%5CFlyFiles%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cbuiltin%5Cglobal-not-found.js&appDir=C%3A%5CUsers%5CMicka%5CCascadeProjects%5CFlyFiles%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMicka%5CCascadeProjects%5CFlyFiles&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/components/navigation.tsx */ \"(rsc)/./src/app/components/navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(rsc)/./src/app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTWlja2FcXENhc2NhZGVQcm9qZWN0c1xcRmx5RmlsZXNcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/components/navigation.tsx":
/*!*******************************************!*\
  !*** ./src/app/components/navigation.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Navigation: () => (/* binding */ Navigation)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const Navigation = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\CascadeProjects\\FlyFiles\\src\\app\\components\\navigation.tsx",
"Navigation",
);

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"242a4118e783\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyNDJhNDExOGU3ODNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/navigation */ \"(rsc)/./src/app/components/navigation.tsx\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"FlyFiles - Dansk fildelingsplatform\",\n    description: \"Send store filer sikkert og nemt med FlyFiles. Dansk fildelingsplatform inspireret af WeTransfer.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"da\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased bg-white`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_3__.Providers, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-h-screen\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation__WEBPACK_IMPORTED_MODULE_2__.Navigation, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-grow\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"border-t border-gray-200 bg-white py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"\\xa9 2025 FlyFiles. Alle rettigheder forbeholdes.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm\",\n                                            children: \"Dansk fildelingsplatform - sikkert og nemt\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\CascadeProjects\\FlyFiles\\src\\app\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/components/navigation.tsx */ \"(ssr)/./src/app/components/navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/components/navigation.tsx":
/*!*******************************************!*\
  !*** ./src/app/components/navigation.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FaChevronDown_FaHome_FaShoppingCart_FaSignOutAlt_FaUpload_FaUser_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaChevronDown,FaHome,FaShoppingCart,FaSignOutAlt,FaUpload,FaUser,FaUserCircle!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var _ui_theme_toggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/theme-toggle */ \"(ssr)/./src/app/components/ui/theme-toggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \n\n\n\n\n\n\n\n\nfunction Navigation() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [activeIndicator, setActiveIndicator] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        left: 0,\n        width: 0,\n        top: 0\n    });\n    const [avatarFailed, setAvatarFailed] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const dropdownRefs = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)({});\n    const navItemRefs = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)({});\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.useSession)();\n    // Check if current page is a light header page\n    const isPricingPage = pathname === '/pricing';\n    const isDashboardPage = pathname === '/dashboard' || pathname.startsWith('/dashboard/');\n    const isLightHeaderPage = isPricingPage || isDashboardPage;\n    const isTextDark = isScrolled || isLightHeaderPage;\n    // Handle avatar loading errors\n    const handleAvatarError = ()=>{\n        setAvatarFailed(true);\n    };\n    // Track scrolling for navbar appearance change\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            if (window.scrollY > 20) {\n                setIsScrolled(true);\n            }\n            const handleScroll = {\n                \"Navigation.useEffect.handleScroll\": ()=>{\n                    if (window.scrollY > 20) {\n                        if (!isScrolled) setIsScrolled(true);\n                    } else {\n                        if (isScrolled) setIsScrolled(false);\n                    }\n                }\n            }[\"Navigation.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Navigation.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], [\n        isScrolled\n    ]);\n    // Close menu when route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            setIsMenuOpen(false);\n            setActiveDropdown(null);\n        }\n    }[\"Navigation.useEffect\"], [\n        pathname\n    ]);\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"Navigation.useEffect.handleClickOutside\": (event)=>{\n                    if (!activeDropdown) return;\n                    const dropdownRef = dropdownRefs.current[activeDropdown];\n                    if (dropdownRef && !dropdownRef.contains(event.target)) {\n                        const isClickOnToggle = event.target instanceof Element && event.target.closest(`button[data-dropdown=\"${activeDropdown}\"]`);\n                        if (!isClickOnToggle) {\n                            setActiveDropdown(null);\n                        }\n                    }\n                }\n            }[\"Navigation.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"Navigation.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], [\n        activeDropdown\n    ]);\n    // Lock body scroll when mobile menu is open\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            if (isMenuOpen) {\n                document.body.style.overflow = 'hidden';\n            } else {\n                document.body.style.overflow = '';\n            }\n            return ({\n                \"Navigation.useEffect\": ()=>{\n                    document.body.style.overflow = '';\n                }\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], [\n        isMenuOpen\n    ]);\n    // Update active indicator position\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            if (true) return;\n            let updateTimeout = null;\n            const debouncedUpdate = {\n                \"Navigation.useEffect.debouncedUpdate\": ()=>{\n                    if (updateTimeout) clearTimeout(updateTimeout);\n                    updateTimeout = setTimeout({\n                        \"Navigation.useEffect.debouncedUpdate\": ()=>{\n                            updateActiveIndicator();\n                        }\n                    }[\"Navigation.useEffect.debouncedUpdate\"], 100);\n                }\n            }[\"Navigation.useEffect.debouncedUpdate\"];\n            updateActiveIndicator();\n            const resizeObserver = new ResizeObserver({\n                \"Navigation.useEffect\": ()=>{\n                    debouncedUpdate();\n                }\n            }[\"Navigation.useEffect\"]);\n            resizeObserver.observe(document.body);\n            return ({\n                \"Navigation.useEffect\": ()=>{\n                    if (updateTimeout) clearTimeout(updateTimeout);\n                    resizeObserver.disconnect();\n                }\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], [\n        pathname,\n        isScrolled\n    ]);\n    // Determines if a link is active\n    const isActive = (path)=>{\n        if (path === '/') {\n            return pathname === '/';\n        }\n        return pathname === path;\n    };\n    // Get the active item key based on the current path\n    const getActiveItemKey = ()=>{\n        if (pathname === '/') {\n            return '';\n        }\n        for (const [key, item] of Object.entries(navLinks)){\n            if (item.type === 'link' && isActive(item.path)) {\n                return key;\n            }\n        }\n        return '';\n    };\n    // Update the active indicator position based on the current pathname\n    const updateActiveIndicator = ()=>{\n        const activeKey = getActiveItemKey();\n        if (!activeKey) {\n            setActiveIndicator({\n                left: 0,\n                width: 0,\n                top: 0\n            });\n            return;\n        }\n        const activeElement = navItemRefs.current[activeKey];\n        if (activeElement) {\n            const rect = activeElement.getBoundingClientRect();\n            setActiveIndicator({\n                left: rect.left,\n                width: rect.width,\n                top: rect.top + rect.height - 2\n            });\n        }\n    };\n    // Toggle dropdown menu\n    const toggleDropdown = (name, event)=>{\n        if (event) {\n            event.stopPropagation();\n        }\n        setActiveDropdown(activeDropdown === name ? null : name);\n    };\n    const handleSignOut = async ()=>{\n        try {\n            setActiveDropdown(null);\n            await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.signOut)({\n                callbackUrl: '/'\n            });\n        } catch (error) {\n            console.error('Error during sign out:', error);\n            window.location.href = '/';\n        }\n    };\n    // Structure for navbar links\n    const navLinks = [\n        {\n            name: 'Forside',\n            path: '/',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaHome_FaShoppingCart_FaSignOutAlt_FaUpload_FaUser_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHome, {\n                className: \"mr-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                lineNumber: 187,\n                columnNumber: 13\n            }, this),\n            type: 'link'\n        },\n        {\n            name: 'Priser',\n            path: '/pricing',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaHome_FaShoppingCart_FaSignOutAlt_FaUpload_FaUser_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaShoppingCart, {\n                className: \"mr-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                lineNumber: 193,\n                columnNumber: 13\n            }, this),\n            type: 'link'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: `fixed w-full z-50 transition-all duration-350 ease-in-out ${isScrolled || isLightHeaderPage ? 'bg-white/95 backdrop-blur-sm text-gray-800 shadow-lg py-2 sm:py-2' : 'bg-transparent text-white py-3 sm:py-4'}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 sm:px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center group z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-2 sm:mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"F\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-lg sm:text-xl font-bold transition-all duration-500 ease-in-out group-hover:translate-x-1 ${isTextDark ? 'text-gray-800' : 'text-white'}`,\n                                        children: \"FlyFiles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-1 lg:space-x-3 z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 lg:space-x-3 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                className: `absolute h-0.5 ${isTextDark ? 'bg-blue-600' : 'bg-white'} rounded-full z-0`,\n                                                initial: false,\n                                                animate: {\n                                                    left: activeIndicator.left,\n                                                    width: activeIndicator.width,\n                                                    top: activeIndicator.top\n                                                },\n                                                transition: {\n                                                    type: \"spring\",\n                                                    stiffness: 350,\n                                                    damping: 30\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            Object.entries(navLinks).map(([key, item], index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.path,\n                                                    ref: (el)=>{\n                                                        navItemRefs.current[key] = el;\n                                                    },\n                                                    className: `font-medium relative px-2 lg:px-3 py-2 rounded-md transition-all duration-300 ease-in-out text-sm lg:text-base ${isActive(item.path) ? isTextDark ? 'text-blue-600' : 'text-white font-bold' : isTextDark ? 'text-gray-800 hover:text-blue-600 hover:bg-blue-50' : 'text-white hover:text-blue-200 hover:bg-white/10'} group`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"md:hidden lg:inline-block\",\n                                                                    children: item.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                item.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `absolute bottom-0 left-0 h-0.5 ${isTextDark ? 'bg-blue-600' : 'bg-white'} transition-all duration-300 ease-in-out ${isActive(item.path) ? 'w-full' : 'w-0 group-hover:w-full'}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this),\n                                    status === \"loading\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this) : session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/dashboard\",\n                                                className: \"ml-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"group relative\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full blur-sm opacity-75 group-hover:opacity-100 transition-opacity duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative bg-gradient-to-b from-blue-600 to-blue-500 text-white px-4 sm:px-5 py-2 rounded-full flex items-center justify-center border-none transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center mr-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaHome_FaShoppingCart_FaSignOutAlt_FaUpload_FaUser_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaUpload, {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Dashboard\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative group ml-1 sm:ml-2 z-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>toggleDropdown('userMenu', e),\n                                                        \"data-dropdown\": \"userMenu\",\n                                                        className: \"flex items-center space-x-2 py-1.5 px-3 rounded-full transition-all duration-300 hover:scale-105 hover:shadow-md\",\n                                                        style: {\n                                                            background: activeDropdown === 'userMenu' ? 'linear-gradient(to bottom right, #4f47e6, #3182ce)' : 'transparent',\n                                                            boxShadow: activeDropdown === 'userMenu' ? '0 4px 12px rgba(66, 153, 225, 0.2)' : 'none'\n                                                        },\n                                                        onMouseEnter: (e)=>{\n                                                            if (activeDropdown !== 'userMenu') {\n                                                                e.currentTarget.style.background = isTextDark ? 'rgba(59, 130, 246, 0.08)' : 'rgba(255, 255, 255, 0.15)';\n                                                            }\n                                                        },\n                                                        onMouseLeave: (e)=>{\n                                                            if (activeDropdown !== 'userMenu') {\n                                                                e.currentTarget.style.background = 'transparent';\n                                                            }\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-8 w-8 rounded-full overflow-hidden border-2 border-blue-400 transition-all duration-300 group-hover:border-blue-500\",\n                                                                children: session.user?.image && !avatarFailed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                    src: session.user.image,\n                                                                    alt: session.user.name || 'User',\n                                                                    width: 32,\n                                                                    height: 32,\n                                                                    className: \"h-full w-full object-cover\",\n                                                                    unoptimized: true,\n                                                                    onError: handleAvatarError\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-full w-full bg-blue-400 flex items-center justify-center text-white group-hover:bg-blue-500 transition-colors duration-300\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaHome_FaShoppingCart_FaSignOutAlt_FaUpload_FaUser_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaUser, {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `hidden md:block text-sm font-medium transition-all duration-300 ${activeDropdown === 'userMenu' ? 'text-white' : isTextDark ? 'text-gray-800 group-hover:text-blue-600' : 'text-white'}`,\n                                                                children: session.user?.name || 'Bruger'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                                animate: {\n                                                                    rotate: activeDropdown === 'userMenu' ? 180 : 0\n                                                                },\n                                                                transition: {\n                                                                    duration: 0.3,\n                                                                    type: \"spring\",\n                                                                    stiffness: 200\n                                                                },\n                                                                className: \"ml-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaHome_FaShoppingCart_FaSignOutAlt_FaUpload_FaUser_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaChevronDown, {\n                                                                    className: `h-3 w-3 ${activeDropdown === 'userMenu' ? 'text-white' : isTextDark ? 'text-gray-600 group-hover:text-blue-600' : 'text-white'}`\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                                                        children: activeDropdown === 'userMenu' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                            initial: {\n                                                                opacity: 0,\n                                                                y: 10,\n                                                                scale: 0.95\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                y: 0,\n                                                                scale: 1\n                                                            },\n                                                            exit: {\n                                                                opacity: 0,\n                                                                y: 10,\n                                                                scale: 0.95\n                                                            },\n                                                            transition: {\n                                                                duration: 0.2\n                                                            },\n                                                            className: \"absolute right-0 mt-2 w-52 bg-white rounded-xl shadow-lg overflow-hidden z-50 border border-blue-100\",\n                                                            ref: (el)=>{\n                                                                dropdownRefs.current['userMenu'] = el;\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"py-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"px-4 py-2 border-b border-gray-100\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm font-medium text-gray-600\",\n                                                                                children: \"Logget ind som\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm font-semibold text-gray-800 truncate\",\n                                                                                children: session.user?.email || session.user?.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                                lineNumber: 359,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                        href: \"/dashboard\",\n                                                                        className: \"flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaHome_FaShoppingCart_FaSignOutAlt_FaUpload_FaUser_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaUserCircle, {\n                                                                                className: \"mr-2 text-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                                lineNumber: 365,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Dashboard\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"border-t border-gray-100 my-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleSignOut,\n                                                                        className: \"flex items-center w-full text-left px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 transition-colors duration-150\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaHome_FaShoppingCart_FaSignOutAlt_FaUpload_FaUser_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaSignOutAlt, {\n                                                                                className: \"mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                                lineNumber: 373,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Log ud\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.signIn)('google'),\n                                        className: \"group relative ml-1 sm:ml-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full blur-sm opacity-75 group-hover:opacity-100 transition-opacity duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative bg-gradient-to-b from-blue-600 to-blue-500 text-white px-4 sm:px-5 py-2 rounded-full flex items-center justify-center border-none transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaHome_FaShoppingCart_FaSignOutAlt_FaUpload_FaUser_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaUser, {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Log ind\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_6__.ThemeToggle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: `md:hidden relative z-60 focus:outline-none transition-all duration-300 ease-in-out p-2 -mr-2`,\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                \"aria-label\": \"Toggle menu\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-6 sm:w-7 h-0.5 my-1.5 transition-all duration-300 ease-in-out ${isMenuOpen ? 'transform rotate-45 translate-y-2 bg-gray-800' : isTextDark ? 'bg-gray-800' : 'bg-white'}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-6 sm:w-7 h-0.5 my-1.5 transition-all duration-300 ease-in-out ${isMenuOpen ? 'opacity-0 bg-gray-800' : isTextDark ? 'bg-gray-800' : 'bg-white'}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-6 sm:w-7 h-0.5 my-1.5 transition-all duration-300 ease-in-out ${isMenuOpen ? 'transform -rotate-45 -translate-y-2 bg-gray-800' : isTextDark ? 'bg-gray-800' : 'bg-white'}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: 100\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        x: 100\n                    },\n                    transition: {\n                        duration: 0.25,\n                        ease: \"easeInOut\"\n                    },\n                    className: \"fixed inset-0 bg-white z-40 overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 pt-20 sm:pt-24 pb-8 h-full flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-1 sm:space-y-2 text-center text-base sm:text-lg\",\n                                children: Object.entries(navLinks).map(([key, item], index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.path,\n                                        className: `py-2.5 sm:py-3 px-4 transition-all duration-300 ease-in-out ${isActive(item.path) ? 'text-blue-600 font-bold' : 'text-gray-800 hover:text-blue-600'}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative group flex justify-center items-center\",\n                                            children: [\n                                                item.icon,\n                                                item.name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `absolute -bottom-1 left-0 h-0.5 bg-blue-600 transition-all duration-300 ease-in-out ${isActive(item.path) ? 'w-full' : 'w-0 sm:group-hover:w-full'}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-auto pb-4 sm:pb-8\",\n                                children: [\n                                    session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 border-t border-gray-100 pt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-16 w-16 rounded-full overflow-hidden border-2 border-blue-400 mr-3\",\n                                                        children: session.user?.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            src: session.user.image,\n                                                            alt: session.user.name || 'User',\n                                                            width: 64,\n                                                            height: 64,\n                                                            className: \"h-full w-full object-cover\",\n                                                            unoptimized: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-full w-full bg-blue-400 flex items-center justify-center text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaHome_FaShoppingCart_FaSignOutAlt_FaUpload_FaUser_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaUser, {\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-bold text-lg text-gray-800\",\n                                                                children: session.user?.name || 'Bruger'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: session.user?.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 gap-3 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/dashboard\",\n                                                    className: \"py-2 px-4 bg-gray-100 text-gray-800 rounded-lg text-center font-medium hover:bg-gray-200 transition-colors\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSignOut,\n                                                className: \"w-full py-3 px-4 bg-red-50 text-red-600 rounded-lg font-bold flex items-center justify-center hover:bg-red-100 transition-colors duration-300 hover:shadow-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaHome_FaShoppingCart_FaSignOutAlt_FaUpload_FaUser_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaSignOutAlt, {\n                                                        className: \"mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Log ud\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 19\n                                    }, this),\n                                    !session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.signIn)('google'),\n                                        className: \"block w-full py-2.5 sm:py-3 px-4 bg-blue-600 text-white text-center rounded-lg font-bold text-base sm:text-lg transition-all duration-300 ease-in-out hover:bg-blue-700 shadow-md hover:shadow-lg transform hover:scale-[1.02] mb-6 flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaHome_FaShoppingCart_FaSignOutAlt_FaUpload_FaUser_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaUser, {\n                                                className: \"mr-2 text-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Log ind med Google\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                    lineNumber: 425,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\navigation.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/ui/button.tsx":
/*!******************************************!*\
  !*** ./src/app/components/ui/button.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/utils */ \"(ssr)/./src/app/lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = 'default', size = 'default', ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group relative inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-bold ring-offset-background transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 overflow-hidden\", {\n            'default': \"bg-gradient-to-b from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:shadow-blue-500/40 hover:scale-105\",\n            'destructive': \"bg-gradient-to-b from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 hover:shadow-xl hover:shadow-red-500/40 hover:scale-105\",\n            'outline': \"border-2 border-blue-600 bg-transparent text-blue-600 hover:bg-gradient-to-b hover:from-blue-600 hover:to-blue-700 hover:text-white hover:shadow-xl hover:shadow-blue-500/40 hover:scale-105 hover:border-blue-700\",\n            'secondary': \"bg-gradient-to-b from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 hover:shadow-xl hover:scale-105\",\n            'ghost': \"hover:bg-gradient-to-b hover:from-blue-50 hover:to-blue-100 text-gray-900 hover:text-blue-600 hover:scale-105\",\n            'link': \"text-blue-600 underline-offset-4 hover:underline hover:text-blue-700\"\n        }[variant], {\n            'default': \"h-10 px-4 py-2\",\n            'sm': \"h-9 rounded-lg px-3\",\n            'lg': \"h-12 rounded-xl px-8 text-base\",\n            'icon': \"h-10 w-10\"\n        }[size], className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 13,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2NvbXBvbmVudHMvdWkvYnV0dG9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBQ007QUFRcEMsTUFBTUUsdUJBQVNGLDZDQUFnQixDQUM3QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsVUFBVSxTQUFTLEVBQUVDLE9BQU8sU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDL0QscUJBQ0UsOERBQUNDO1FBQ0NMLFdBQVdILGtEQUFFQSxDQUNYLDJXQUNBO1lBQ0UsV0FBVztZQUNYLGVBQWU7WUFDZixXQUFXO1lBQ1gsYUFBYTtZQUNiLFNBQVM7WUFDVCxRQUFRO1FBQ1YsQ0FBQyxDQUFDSSxRQUFRLEVBQ1Y7WUFDRSxXQUFXO1lBQ1gsTUFBTTtZQUNOLE1BQU07WUFDTixRQUFRO1FBQ1YsQ0FBQyxDQUFDQyxLQUFLLEVBQ1BGO1FBRUZJLEtBQUtBO1FBQ0osR0FBR0QsS0FBSzs7Ozs7O0FBR2Y7QUFFRkwsT0FBT1EsV0FBVyxHQUFHO0FBRUoiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTWlja2FcXENhc2NhZGVQcm9qZWN0c1xcRmx5RmlsZXNcXHNyY1xcYXBwXFxjb21wb25lbnRzXFx1aVxcYnV0dG9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxyXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2FwcC9saWIvdXRpbHNcIlxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBCdXR0b25Qcm9wc1xyXG4gIGV4dGVuZHMgUmVhY3QuQnV0dG9uSFRNTEF0dHJpYnV0ZXM8SFRNTEJ1dHRvbkVsZW1lbnQ+IHtcclxuICB2YXJpYW50PzogJ2RlZmF1bHQnIHwgJ2Rlc3RydWN0aXZlJyB8ICdvdXRsaW5lJyB8ICdzZWNvbmRhcnknIHwgJ2dob3N0JyB8ICdsaW5rJ1xyXG4gIHNpemU/OiAnZGVmYXVsdCcgfCAnc20nIHwgJ2xnJyB8ICdpY29uJ1xyXG59XHJcblxyXG5jb25zdCBCdXR0b24gPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxCdXR0b25FbGVtZW50LCBCdXR0b25Qcm9wcz4oXHJcbiAgKHsgY2xhc3NOYW1lLCB2YXJpYW50ID0gJ2RlZmF1bHQnLCBzaXplID0gJ2RlZmF1bHQnLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxidXR0b25cclxuICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgXCJncm91cCByZWxhdGl2ZSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgd2hpdGVzcGFjZS1ub3dyYXAgcm91bmRlZC14bCB0ZXh0LXNtIGZvbnQtYm9sZCByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBlYXNlLVtjdWJpYy1iZXppZXIoMC42OCwtMC41NSwwLjI3LDEuNTUpXSBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwIG92ZXJmbG93LWhpZGRlblwiLFxyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICAnZGVmYXVsdCc6IFwiYmctZ3JhZGllbnQtdG8tYiBmcm9tLWJsdWUtNjAwIHRvLWJsdWUtNzAwIHRleHQtd2hpdGUgaG92ZXI6ZnJvbS1ibHVlLTcwMCBob3Zlcjp0by1ibHVlLTgwMCBob3ZlcjpzaGFkb3cteGwgaG92ZXI6c2hhZG93LWJsdWUtNTAwLzQwIGhvdmVyOnNjYWxlLTEwNVwiLFxyXG4gICAgICAgICAgICAnZGVzdHJ1Y3RpdmUnOiBcImJnLWdyYWRpZW50LXRvLWIgZnJvbS1yZWQtNjAwIHRvLXJlZC03MDAgdGV4dC13aGl0ZSBob3Zlcjpmcm9tLXJlZC03MDAgaG92ZXI6dG8tcmVkLTgwMCBob3ZlcjpzaGFkb3cteGwgaG92ZXI6c2hhZG93LXJlZC01MDAvNDAgaG92ZXI6c2NhbGUtMTA1XCIsXHJcbiAgICAgICAgICAgICdvdXRsaW5lJzogXCJib3JkZXItMiBib3JkZXItYmx1ZS02MDAgYmctdHJhbnNwYXJlbnQgdGV4dC1ibHVlLTYwMCBob3ZlcjpiZy1ncmFkaWVudC10by1iIGhvdmVyOmZyb20tYmx1ZS02MDAgaG92ZXI6dG8tYmx1ZS03MDAgaG92ZXI6dGV4dC13aGl0ZSBob3ZlcjpzaGFkb3cteGwgaG92ZXI6c2hhZG93LWJsdWUtNTAwLzQwIGhvdmVyOnNjYWxlLTEwNSBob3Zlcjpib3JkZXItYmx1ZS03MDBcIixcclxuICAgICAgICAgICAgJ3NlY29uZGFyeSc6IFwiYmctZ3JhZGllbnQtdG8tYiBmcm9tLWdyYXktMTAwIHRvLWdyYXktMjAwIHRleHQtZ3JheS05MDAgaG92ZXI6ZnJvbS1ncmF5LTIwMCBob3Zlcjp0by1ncmF5LTMwMCBob3ZlcjpzaGFkb3cteGwgaG92ZXI6c2NhbGUtMTA1XCIsXHJcbiAgICAgICAgICAgICdnaG9zdCc6IFwiaG92ZXI6YmctZ3JhZGllbnQtdG8tYiBob3Zlcjpmcm9tLWJsdWUtNTAgaG92ZXI6dG8tYmx1ZS0xMDAgdGV4dC1ncmF5LTkwMCBob3Zlcjp0ZXh0LWJsdWUtNjAwIGhvdmVyOnNjYWxlLTEwNVwiLFxyXG4gICAgICAgICAgICAnbGluayc6IFwidGV4dC1ibHVlLTYwMCB1bmRlcmxpbmUtb2Zmc2V0LTQgaG92ZXI6dW5kZXJsaW5lIGhvdmVyOnRleHQtYmx1ZS03MDBcIlxyXG4gICAgICAgICAgfVt2YXJpYW50XSxcclxuICAgICAgICAgIHtcclxuICAgICAgICAgICAgJ2RlZmF1bHQnOiBcImgtMTAgcHgtNCBweS0yXCIsXHJcbiAgICAgICAgICAgICdzbSc6IFwiaC05IHJvdW5kZWQtbGcgcHgtM1wiLFxyXG4gICAgICAgICAgICAnbGcnOiBcImgtMTIgcm91bmRlZC14bCBweC04IHRleHQtYmFzZVwiLFxyXG4gICAgICAgICAgICAnaWNvbic6IFwiaC0xMCB3LTEwXCJcclxuICAgICAgICAgIH1bc2l6ZV0sXHJcbiAgICAgICAgICBjbGFzc05hbWVcclxuICAgICAgICApfVxyXG4gICAgICAgIHJlZj17cmVmfVxyXG4gICAgICAgIHsuLi5wcm9wc31cclxuICAgICAgLz5cclxuICAgIClcclxuICB9XHJcbilcclxuQnV0dG9uLmRpc3BsYXlOYW1lID0gXCJCdXR0b25cIlxyXG5cclxuZXhwb3J0IHsgQnV0dG9uIH0gIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJCdXR0b24iLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidmFyaWFudCIsInNpemUiLCJwcm9wcyIsInJlZiIsImJ1dHRvbiIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/ui/theme-toggle.tsx":
/*!************************************************!*\
  !*** ./src/app/components/ui/theme-toggle.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./button */ \"(ssr)/./src/app/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ThemeToggle auto */ \n\n\n\nfunction ThemeToggle() {\n    const [theme, setTheme] = react__WEBPACK_IMPORTED_MODULE_1__.useState('system');\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ThemeToggle.useEffect\": ()=>{\n            setMounted(true);\n            const savedTheme = localStorage.getItem('theme') || 'system';\n            setTheme(savedTheme);\n            applyTheme(savedTheme);\n        }\n    }[\"ThemeToggle.useEffect\"], []);\n    const applyTheme = (newTheme)=>{\n        const root = window.document.documentElement;\n        root.classList.remove('light', 'dark');\n        if (newTheme === 'system') {\n            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n            root.classList.add(systemTheme);\n        } else {\n            root.classList.add(newTheme);\n        }\n    };\n    const toggleTheme = ()=>{\n        const newTheme = theme === 'light' ? 'dark' : theme === 'dark' ? 'system' : 'light';\n        setTheme(newTheme);\n        localStorage.setItem('theme', newTheme);\n        applyTheme(newTheme);\n    };\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n            variant: \"ghost\",\n            size: \"icon\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\ui\\\\theme-toggle.tsx\",\n                lineNumber: 38,\n                columnNumber: 48\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\ui\\\\theme-toggle.tsx\",\n            lineNumber: 38,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n        variant: \"ghost\",\n        size: \"icon\",\n        onClick: toggleTheme,\n        title: `Current theme: ${theme}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\ui\\\\theme-toggle.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\ui\\\\theme-toggle.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle theme\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\ui\\\\theme-toggle.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\components\\\\ui\\\\theme-toggle.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/ui/theme-toggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/lib/plans.ts":
/*!******************************!*\
  !*** ./src/app/lib/plans.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGS: () => (/* binding */ PLAN_CONFIGS)\n/* harmony export */ });\n// Plan configurations - separated from MongoDB to avoid client-side bundling issues\nconst PLAN_CONFIGS = {\n    guest: {\n        name: 'Gæst',\n        uploadLimit: {\n            amount: 250 * 1024 * 1024,\n            period: 'session'\n        },\n        fileExpiry: 7,\n        downloadLimits: {\n            configurable: false,\n            unlimited: true\n        },\n        features: [\n            '250MB pr. session',\n            '7 dages filudløb',\n            'Ubegrænsede downloads',\n            'Ingen konto nødvendig'\n        ]\n    },\n    free: {\n        name: 'Gratis Konto',\n        uploadLimit: {\n            amount: 15 * 1024 * 1024 * 1024,\n            period: 'måned'\n        },\n        fileExpiry: 10,\n        downloadLimits: {\n            configurable: true,\n            unlimited: false\n        },\n        features: [\n            '15GB månedlig upload',\n            '10-dages filudløb',\n            'Konfigurerbare downloadgrænser',\n            'Google login påkrævet'\n        ]\n    },\n    upgrade1: {\n        name: 'Opgradering 1',\n        uploadLimit: {\n            amount: 15 * 1024 * 1024 * 1024,\n            period: 'uge'\n        },\n        fileExpiry: 14,\n        downloadLimits: {\n            configurable: true,\n            unlimited: false\n        },\n        features: [\n            '15GB ugentlig upload',\n            '14-dages filudløb',\n            'Download statistik',\n            'Alle Gratis funktioner'\n        ],\n        price: {\n            amount: 5,\n            period: 'måned'\n        }\n    },\n    upgrade2: {\n        name: 'Opgradering 2',\n        uploadLimit: {\n            amount: 50 * 1024 * 1024 * 1024,\n            period: 'uge'\n        },\n        fileExpiry: 30,\n        downloadLimits: {\n            configurable: true,\n            unlimited: true\n        },\n        features: [\n            '50GB ugentlig upload',\n            '30-dages filudløb',\n            'Ubegrænsede downloads',\n            'Avanceret analyse',\n            'Alle tidligere funktioner'\n        ],\n        price: {\n            amount: 25,\n            period: 'måned'\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/lib/plans.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/lib/utils.ts":
/*!******************************!*\
  !*** ./src/app/lib/utils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateUsagePercentage: () => (/* binding */ calculateUsagePercentage),\n/* harmony export */   canUserUpload: () => (/* binding */ canUserUpload),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   generateFileId: () => (/* binding */ generateFileId),\n/* harmony export */   generateSessionId: () => (/* binding */ generateSessionId),\n/* harmony export */   getCurrentPeriodStart: () => (/* binding */ getCurrentPeriodStart),\n/* harmony export */   getFileExpiryDate: () => (/* binding */ getFileExpiryDate),\n/* harmony export */   getPlanDisplayName: () => (/* binding */ getPlanDisplayName),\n/* harmony export */   getStoredTheme: () => (/* binding */ getStoredTheme),\n/* harmony export */   isValidFileType: () => (/* binding */ isValidFileType),\n/* harmony export */   setStoredTheme: () => (/* binding */ setStoredTheme)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* harmony import */ var _plans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./plans */ \"(ssr)/./src/app/lib/plans.ts\");\n\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_2__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Format file size\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = [\n        'B',\n        'KB',\n        'MB',\n        'GB',\n        'TB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n// Format date to Danish format\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat('da-DK', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    }).format(new Date(date));\n}\n// Get plan display name\nfunction getPlanDisplayName(plan) {\n    return _plans__WEBPACK_IMPORTED_MODULE_1__.PLAN_CONFIGS[plan].name;\n}\n// Calculate usage percentage\nfunction calculateUsagePercentage(used, limit) {\n    if (limit === 0) return 0;\n    return Math.min(used / limit * 100, 100);\n}\n// Generate random session ID for guest users\nfunction generateSessionId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\n// Generate random file ID\nfunction generateFileId() {\n    return Math.random().toString(36).substring(2, 15) + Date.now().toString(36);\n}\n// Get file expiry date based on plan\nfunction getFileExpiryDate(plan) {\n    const days = _plans__WEBPACK_IMPORTED_MODULE_1__.PLAN_CONFIGS[plan].fileExpiry;\n    const expiryDate = new Date();\n    expiryDate.setDate(expiryDate.getDate() + days);\n    return expiryDate;\n}\n// Check if user can upload based on plan limits\nfunction canUserUpload(plan, currentUsage, fileSize) {\n    const planConfig = _plans__WEBPACK_IMPORTED_MODULE_1__.PLAN_CONFIGS[plan];\n    const limit = planConfig.uploadLimit.amount;\n    if (currentUsage + fileSize > limit) {\n        return {\n            canUpload: false,\n            reason: `File would exceed ${formatFileSize(limit)} ${planConfig.uploadLimit.period} limit`\n        };\n    }\n    return {\n        canUpload: true\n    };\n}\n// Get current period start date\nfunction getCurrentPeriodStart(period) {\n    const now = new Date();\n    if (period === 'week') {\n        // Monday as start of week\n        const monday = new Date(now);\n        const day = monday.getDay();\n        const diff = monday.getDate() - day + (day === 0 ? -6 : 1);\n        monday.setDate(diff);\n        monday.setHours(0, 0, 0, 0);\n        return monday;\n    } else {\n        // First day of month\n        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);\n        firstDay.setHours(0, 0, 0, 0);\n        return firstDay;\n    }\n}\n// Validate file type (basic validation)\nfunction isValidFileType(mimeType) {\n    // Allow most common file types, block potentially dangerous ones\n    const blockedTypes = [\n        'application/x-executable',\n        'application/x-msdownload',\n        'application/x-msdos-program'\n    ];\n    return !blockedTypes.includes(mimeType);\n}\n// Get theme from localStorage or default\nfunction getStoredTheme() {\n    if (true) return 'system';\n    return localStorage.getItem('theme') || 'system';\n}\n// Set theme in localStorage\nfunction setStoredTheme(theme) {\n    if (true) return;\n    localStorage.setItem('theme', theme);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFaUQ7QUFFMUMsU0FBU0MsVUFBVSxFQUFFQyxRQUFRLEVBQWlDO0lBQ25FLHFCQUNFLDhEQUFDRiw0REFBZUE7a0JBQ2JFOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNaWNrYVxcQ2FzY2FkZVByb2plY3RzXFxGbHlGaWxlc1xcc3JjXFxhcHBcXHByb3ZpZGVycy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcclxuXHJcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gXCJuZXh0LWF1dGgvcmVhY3RcIlxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxTZXNzaW9uUHJvdmlkZXI+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvU2Vzc2lvblByb3ZpZGVyPlxyXG4gIClcclxufSAiXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/lucide-react","vendor-chunks/react-icons","vendor-chunks/next-auth","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/@auth"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=C%3A%5CUsers%5CMicka%5CCascadeProjects%5CFlyFiles%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cbuiltin%5Cglobal-not-found.js&appDir=C%3A%5CUsers%5CMicka%5CCascadeProjects%5CFlyFiles%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMicka%5CCascadeProjects%5CFlyFiles&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();
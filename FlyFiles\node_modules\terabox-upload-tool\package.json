{"name": "terabox-upload-tool", "version": "1.3.1", "description": "A robust library designed for seamless integration with TeraBox, the leading cloud storage service offering 1 TB of free space. Effortlessly upload files, download files, delete files, manage directories, and retrieve file lists. Ideal for developers seeking efficient cloud storage solutions within their Node.js projects.", "repository": {"type": "git", "url": "git+https://github.com/Pahadi10/terabox-upload-tool"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["terabox", "upload", "file", "storage", "node"], "homepage": "https://github.com/Pahadi10/terabox-upload-tool#readme", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"axios": "^1.7.9", "form-data": "^4.0.1"}}
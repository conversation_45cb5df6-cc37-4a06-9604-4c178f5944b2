import { NextRequest, NextResponse } from 'next/server';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { fileCode: string } }
) {
  try {
    const { fileCode } = params;

    if (!fileCode) {
      return NextResponse.json(
        { error: 'File code is required' },
        { status: 400 }
      );
    }

    const baseUrl = process.env.GOFILE_API_BASE_URL || 'https://api.gofile.io';
    const accountToken = process.env.GOFILE_ACCOUNT_TOKEN;

    if (!accountToken) {
      return NextResponse.json(
        { error: 'Account token is required for file deletion' },
        { status: 401 }
      );
    }

    // Build URL with token
    const url = `${baseUrl}/deleteFile/${fileCode}?token=${accountToken}`;

    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to delete file', details: result },
        { status: response.status }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Delete file error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

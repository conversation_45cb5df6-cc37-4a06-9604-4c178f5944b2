"use client"

import { ShieldCheck, Lock, Database, EyeOff, Server, X } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useState, useEffect } from "react"

interface SecureFilesModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SecureFiles({ isOpen, onClose }: SecureFilesModalProps) {
  const [isClosing, setIsClosing] = useState(false)

  useEffect(() => {
    if (isOpen) {
      // Calculate width before hiding scrollbar to prevent layout shift
      const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth
      document.body.style.overflow = 'hidden'
      document.body.style.paddingRight = `${scrollbarWidth}px`
    } else {
      document.body.style.overflow = 'auto'
      document.body.style.paddingRight = '0px'
    }

    return () => {
      document.body.style.overflow = 'auto'
      document.body.style.paddingRight = '0px'
    }
  }, [isOpen])

  const handleClose = () => {
    setIsClosing(true)
    setTimeout(() => {
      onClose()
      setIsClosing(false)
    }, 100)
  }

  const securityFeatures = [
    {
      icon: <Lock className="h-5 w-5" />,
      title: "Kryptering under transport og opbevaring",
      description: "Alle filer krypteres med TLS under overførsel og AES-256 ved opbevaring."
    },
    {
      icon: <Database className="h-5 w-5" />,
      title: "Ingen dataanalyse eller AI-træning",
      description: "Vi analyserer ikke dine filers indhold og bruger dem ikke til at træne AI-modeller."
    },
    {
      icon: <EyeOff className="h-5 w-5" />,
      title: "Ingen salg til tredjeparter",
      description: "Vi deler eller sælger ikke dine data med nogen tredjepart - aldrig."
    },
    {
      icon: <Server className="h-5 w-5" />,
      title: "Data i Danmark",
      description: "Alle data opbevares i Danmark og er underlagt dansk lovgivning."
    },
    {
      icon: <ShieldCheck className="h-5 w-5" />,
      title: "Automatisk sletning",
      description: "Filers levetid styres af din plan, og de slettes automatisk efter udløb."
    }
  ]

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div 
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.15 }}
        >
          <motion.div
            className="fixed inset-0 bg-black/70 backdrop-blur-sm"
            onClick={handleClose}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
          />

          <motion.div
            className="relative bg-gradient-to-b from-blue-50 to-white w-full max-w-lg mx-auto rounded-2xl shadow-2xl p-8 z-10 max-h-[90vh] overflow-y-auto border border-gray-200"
            initial={{ opacity: 0, y: 10, scale: 0.98 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.98 }}
            transition={{ duration: 0.15, ease: "easeOut" }}
          >
            <motion.button
              onClick={handleClose}
              className="absolute top-5 right-5 text-gray-500 hover:text-gray-700 transition-colors p-1 rounded-full hover:bg-gray-100"
              aria-label="Luk"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <X size={20} />
            </motion.button>

            <motion.div 
              className="mb-8 text-center"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.05 }}
            >
              <motion.div 
                className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4"
                whileHover={{ scale: 1.05 }}
              >
                <ShieldCheck className="h-8 w-8 text-blue-600" />
              </motion.div>
              <h2 className="text-3xl font-bold text-gray-900">Sikkerhed og privatliv</h2>
              <p className="text-gray-600 mt-2">
                Sådan beskytter vi dine filer og data
              </p>
            </motion.div>

            <div className="space-y-5">
              {securityFeatures.map((feature, index) => (
                <motion.div 
                  key={index} 
                  className="flex items-start p-4 bg-white rounded-xl shadow-sm border border-gray-100"
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 + index * 0.03 }}
                  whileHover={{ y: -1 }}
                >
                  <motion.div 
                    className="p-2.5 bg-blue-50 rounded-lg text-blue-600 mr-4"
                    whileHover={{ rotate: 3 }}
                  >
                    {feature.icon}
                  </motion.div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{feature.title}</h3>
                    <p className="text-gray-600 text-sm mt-1.5">
                      {feature.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
